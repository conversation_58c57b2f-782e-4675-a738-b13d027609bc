{"name": "cartrack", "version": "0.0.0", "license": "MIT", "type": "module", "scripts": {"start": "corepack enable pnpm && pnpm install --frozen-lockfile && nx serve", "build": "nx build", "test": "nx test", "lint": "nx lint", "e2e": "nx e2e", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "clean-start": "npx rimraf node_modules && pnpm start", "tsc-check": "nx tsc-check", "css-lint": "nx css-lint", "update-locales": "nx update-locales", "circular-check": "npx madge --circular --extensions ts,tsx,js,jsx ./", "storybook": "cross-env NODE_OPTIONS=--max-old-space-size=4096 nx storybook", "workspace-generator": "nx workspace-generator", "karoo-ui-core-watch": "nx run karoo-ui-core:build:watch", "karoo-ui-core-storybook": "nx run karoo-ui-core:storybook", "fleet-web-component-test-run": "nx run fleet-web:component-test", "postinstall": "husky install"}, "private": true, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"scope-case": [2, "always", ["camel-case", "pascal-case", "kebab-case"]]}}, "config": {"commitizen": {"path": "@commitlint/prompt"}}, "dependencies": {"@ahooksjs/use-url-state": "3.5.0", "@changey/react-leaflet-markercluster": "4.0.0-rc1", "@emotion/react": "11.11.4", "@emotion/styled": "11.11.5", "@formatjs/intl-locale": "4.2.4", "@fortawesome/fontawesome-svg-core": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/fontawesome-svg-core", "@fortawesome/free-regular-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/free-regular-svg-icons", "@fortawesome/free-solid-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/free-solid-svg-icons", "@fortawesome/pro-light-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/pro-light-svg-icons", "@fortawesome/pro-regular-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/pro-regular-svg-icons", "@fortawesome/pro-solid-svg-icons": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/pro-solid-svg-icons", "@fortawesome/react-fontawesome": "file:apps/fleet-web/src/_third-party-libs-forks/@fortawesome/react-fontawesome", "@geoman-io/leaflet-geoman-free": "2.15.0", "@googlemaps/js-api-loader": "1.16.6", "@hookform/resolvers": "3.1.0", "@microsoft/signalr": "8.0.7", "@mobily/ts-belt": "3.13.1", "@mui/icons-material": "7.3.1", "@mui/lab": "7.0.0-beta.16", "@mui/material": "7.3.1", "@mui/system": "7.3.1", "@mui/types": "7.4.5", "@mui/x-data-grid": "8.11.1", "@mui/x-data-grid-generator": "8.11.1", "@mui/x-data-grid-premium": "8.11.1", "@mui/x-data-grid-pro": "8.11.1", "@mui/x-date-pickers": "7.16.0", "@mui/x-date-pickers-pro": "7.16.0", "@mui/x-license": "7.16.0", "@mui/x-tree-view": "6.17.0", "@popperjs/core": "2.11.8", "@react-dnd/shallowequal": "2.0.0", "@react-hook/resize-observer": "1.2.6", "@react-pdf/renderer": "3.4.4", "@react-spring/web": "9.7.2", "@reduxjs/toolkit": "1.3.1", "@sentry/browser": "7.118.0", "@tanstack/react-form": "1.19.1", "@tanstack/react-query": "5.8.4", "@tanstack/react-query-devtools": "5.8.4", "@tippyjs/react": "4.0.0", "@toast-ui/calendar": "2.1.3", "@toast-ui/react-calendar": "2.1.3", "@trainiac/html2canvas": "1.0.0", "@turf/boolean-point-in-polygon": "6.5.0", "@turf/helpers": "6.5.0", "@types/codemirror": "5.60.16", "@vis.gl/react-google-maps": "1.5.3", "@visx/axis": "3.12.0", "@visx/brush": "3.12.0", "@visx/curve": "3.12.0", "@visx/event": "3.12.0", "@visx/glyph": "3.12.0", "@visx/gradient": "3.12.0", "@visx/grid": "3.12.0", "@visx/group": "3.12.0", "@visx/responsive": "3.12.0", "@visx/scale": "3.12.0", "@visx/shape": "3.12.0", "@visx/tooltip": "3.12.0", "@visx/xychart": "3.12.0", "bignumber.js": "9.0.0", "broadcast-channel": "4.2.0", "classnames": "2.3.1", "codemirror": "5.65.16", "connected-react-router": "6.9.3", "core-js": "~3.37.0", "cropperjs": "1.5.13", "csstype": "3.0.10", "d3-array": "3.0.2", "date-fns": "2.29.3", "dequal": "2.0.3", "dompurify": "2.4.0", "downloadjs": "1.4.7", "downshift": "3.2.10", "esri-leaflet": "3.0.12", "esri-leaflet-vector": "4.2.3", "exceljs": "4.4.0", "file-saver": "2.0.5", "flexboxgrid": "6.3.1", "formik": "2.2.9", "google-map-react": "2.2.1", "history": "4.10.1", "humanize-duration": "3.27.1", "idb-keyval": "6.1.0", "immer": "6.0.9", "in-view": "0.6.1", "jsbarcode": "3.11.5", "json-stable-stringify": "1.1.1", "jspdf": "3.0.2", "jszip": "3.5.0", "jwt-decode": "4.0.0", "konva": "8.4.2", "leaflet": "1.9.4", "leaflet-editable": "1.2.0", "leaflet-path-drag": "1.9.5", "leaflet.markercluster": "1.5.3", "lodash": "~4.17.21", "luxon": "3.4.4", "match-sorter": "2.3.0", "material-ui-popup-state": "4.1.0", "memoize-one": "5.1.1", "moment": "2.29.4", "moment-timezone": "0.5.43", "notistack": "3.0.1", "pdf-lib": "1.17.1", "pdfjs-dist": "3.11.174", "points-cluster": "0.1.4", "polished": "4.1.0", "process": "0.11.10", "prop-types": "15.7.2", "qrcode": "1.5.3", "qrcode.react": "4.2.0", "query-string": "6.14.1", "react": "18.3.1", "react-click-outside": "2.3.1", "react-codemirror2": "8.0.1", "react-compiler-runtime": "19.1.0-rc.3", "react-content-loader": "7.0.0", "react-copy-to-clipboard": "5.0.4", "react-cropper": "2.1.8", "react-day-picker": "6.2.1", "react-device-detect": "1.17.0", "react-dnd": "14.0.5", "react-dnd-html5-backend": "14.1.0", "react-dom": "18.3.1", "react-dropzone": "14.2.3", "react-hook-form": "7.52.1", "react-imask": "7.5.0", "react-intl": "6.8.7", "react-konva": "18.2.4", "react-leaflet": "4.2.1", "react-modal": "3.12.1", "react-onclickoutside": "6.8.0", "react-otp-input": "3.0.2", "react-phone-number-input": "3.2.9", "react-player": "2.9.0", "react-pro-sidebar": "1.1.0", "react-redux": "7.2.5", "react-router": "5.2.1", "react-router-dom": "5.3.0", "react-select": "3.0.8", "react-slick": "0.30.2", "react-split": "2.0.12", "react-spring": "9.2.4", "react-switch": "6.0.0", "react-table": "6.8.6", "react-tiny-virtual-list": "2.2.0", "react-toastify": "10.0.5", "react-transition-group": "2.9.0", "react-virtualized": "9.22.5", "react-virtualized-auto-sizer": "1.0.24", "react-window": "1.8.7", "recharts": "1.5.0", "redux-form": "8.3.6", "redux-persist": "6.0.0", "redux-saga": "1.1.3", "remeda": "2.30.0", "reselect": "5.1.0", "screenfull": "4.2.0", "slick-carousel": "1.8.1", "stream-browserify": "3.0.0", "styled-components": "5.3.5", "tippy.js": "6.3.1", "ts-pattern": "5.4.0", "tslib": "^2.4.0", "type-fest": "4.26.0", "typed-redux-saga": "1.3.1", "use-effect-reducer": "0.7.0", "use-places-autocomplete": "4.0.0", "uuid": "11.1.0", "virtua": "0.42.0", "vm-browserify": "1.1.2", "xlsx": "0.16.0", "yup": "0.27.0", "zenscroll": "4.0.2", "zod": "3.25.56"}, "devDependencies": {"@4tw/cypress-drag-drop": "2.2.5", "@babel/core": "7.24.7", "@babel/plugin-syntax-jsx": "7.24.7", "@babel/plugin-syntax-typescript": "7.24.7", "@babel/preset-typescript": "7.24.7", "@commitlint/cli": "9.1.2", "@commitlint/config-conventional": "9.1.2", "@commitlint/prompt": "9.1.2", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.28.0", "@fsouza/prettierd": "0.25.3", "@ianvs/prettier-plugin-sort-imports": "4.2.1", "@jambit/eslint-plugin-typed-redux-saga": "0.4.0", "@nx/cypress": "21.3.11", "@nx/eslint": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/react": "21.3.11", "@nx/rollup": "21.3.11", "@nx/storybook": "21.3.11", "@nx/vite": "21.3.11", "@nx/web": "21.3.11", "@nx/webpack": "21.3.11", "@rspack/cli": "1.5.2", "@rspack/core": "1.5.2", "@rspack/plugin-react-refresh": "1.4.3", "@storybook/addon-essentials": "8.1.11", "@storybook/addon-interactions": "8.1.11", "@storybook/components": "8.1.11", "@storybook/core-events": "8.1.11", "@storybook/core-server": "8.1.11", "@storybook/manager-api": "8.1.11", "@storybook/react": "8.1.11", "@storybook/react-webpack5": "8.1.11", "@storybook/test": "8.1.11", "@storybook/theming": "8.1.11", "@storybook/types": "8.1.11", "@swc-node/register": "1.10.0", "@swc/cli": "0.7.8", "@swc/core": "1.7.23", "@swc/helpers": "0.5.17", "@testing-library/cypress": "10.0.2", "@types/d3-array": "3.0.1", "@types/d3-scale": "3.3.2", "@types/dompurify": "2.3.4", "@types/downloadjs": "1.4.1", "@types/esri-leaflet": "3.0.3", "@types/file-saver": "2.0.5", "@types/geojson": "7946.0.7", "@types/google-map-react": "2.1.10", "@types/google.maps": "3.55.10", "@types/history": "4.7.9", "@types/humanize-duration": "3.27.1", "@types/json-stable-stringify": "1.0.36", "@types/leaflet": "1.9.12", "@types/leaflet.markercluster": "1.5.4", "@types/lodash": "4.14.182", "@types/luxon": "3.4.2", "@types/match-sorter": "2.3.0", "@types/node": "18.19.14", "@types/prop-types": "15.7.5", "@types/qrcode": "1.5.0", "@types/react": "18.3.2", "@types/react-copy-to-clipboard": "5.0.2", "@types/react-dom": "18.3.0", "@types/react-modal": "3.12.0", "@types/react-redux": "~7.1.24", "@types/react-router": "5.1.18", "@types/react-router-dom": "5.3.3", "@types/react-select": "3.0.19", "@types/react-slick": "0.23.13", "@types/react-table": "6.8.5", "@types/react-transition-group": "2.9.0", "@types/react-virtualized": "9.21.30", "@types/react-virtualized-auto-sizer": "1.0.4", "@types/react-window": "1.8.5", "@types/recharts": "1.8.18", "@types/redux-form": "8.3.0", "@types/styled-components": "5.1.26", "@types/uuid": "9.0.8", "@types/yup": "0.26.37", "@types/zenscroll": "4.0.0", "@typescript-eslint/eslint-plugin": "8.39.0", "@typescript-eslint/parser": "8.39.0", "@vitejs/plugin-react": "4.3.4", "@vitejs/plugin-react-swc": "3.7.2", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "@vitest/eslint-plugin": "1.1.38", "@vitest/ui": "3.2.4", "babel-loader": "9.2.1", "babel-plugin-react-compiler": "19.1.0-rc.3", "caniuse-lite": "1.0.30001636", "commitizen": "2.10.1", "cross-env": "5.2.0", "cypress": "15.1.0", "cypress-multi-reporters": "1.6.4", "cypress-parallel": "0.15.0", "cypress-rspack-dev-server": "1.1.1", "dotenv": "10.0.0", "env-cmd": "10.1.0", "esbuild-plugin-react-virtualized": "1.0.5", "eslint": "9.8.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-cypress": "4.2.0", "eslint-plugin-googlemaps": "1.4.15", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-no-only-tests": "3.1.0", "eslint-plugin-no-unsanitized": "4.1.2", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-compiler": "19.1.0-rc.2", "eslint-plugin-react-hooks": "0.0.0-experimental-72135096-20250421", "eslint-plugin-risxss": "2.1.0", "eslint-plugin-sonarjs": "2.0.3", "eslint-plugin-storybook": "0.10.1", "eslint-plugin-unicorn": "55.0.0", "eslint-plugin-vitest": "0.5.4", "eslint-plugin-xss": "0.1.12", "husky": "7.0.2", "jsdom": "24.0.0", "lint-staged": "10.5.2", "npm-run-all": "4.1.5", "nx": "21.3.11", "oxlint": "1.9.0", "playwright": "1.49.1", "postcss": "8.4.38", "postcss-focus": "5.0.1", "postcss-loader": "7.3.3", "postcss-preset-env": "7.8.3", "postcss-styled-syntax": "0.5.0", "prettier": "3.1.1", "react-refresh": "0.17.0", "rimraf": "5.0.7", "sass": "1.44.0", "sass-loader": "16.0.5", "storybook": "8.1.11", "svg-inline-loader": "0.8.2", "typescript": "5.9.2", "typescript-eslint": "8.39.0", "vite": "7.0.6", "vite-plugin-dts": "4.5.4", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.4", "vitest-browser-react": "1.0.1", "webpack": "5.98.0"}, "pnpm": {"patchedDependencies": {"redux-persist@6.0.0": "patches/<EMAIL>", "formik@2.2.9": "patches/<EMAIL>", "google-map-react@2.2.1": "patches/<EMAIL>", "react-hook-form@7.52.1": "patches/<EMAIL>"}, "onlyBuiltDependencies": ["@fortawesome/pro-light-svg-icons", "@fortawesome/pro-regular-svg-icons", "@fortawesome/pro-solid-svg-icons", "@posthog/cli", "@sentry/cli", "@swc/core", "core-js", "core-js-pure", "cypress", "esbuild", "msw", "nx"], "ignoredBuiltDependencies": ["@fortawesome/fontawesome-common-types", "@parcel/watcher", "@react-spring/core", "@scarf/scarf", "canvas", "commitizen", "spawn-sync", "styled-components"]}, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b"}