import { MiFleetDatePicker } from 'src/modules/mifleet/components/date-picker/MiFleetDatePicker'
import { useMifleetFormattedNumber } from 'src/modules/mifleet/shared/utils'

import AdminHeader from './admin-header'
import AdvancedTable from './advanced-table'
import AlertBar from './alert-bar'
import AlertButton from './alert-button'
import AlertsBadge from './alerts-badge'
import Breadcrumbs from './breadcrumbs'
import Button from './button'
import Carousel from './carousel'
import Checkbox from './checkbox'
import ClassWrapper from './class-wrapper'
import CollapsedList from './collapsed-list'
import colorOptions from './color-options'
import ContainerHeader from './container/components/header/header'
import Container from './container/container'
import copyToClipboard from './copy-to-clipboard'
import Alert from './custom/Alert'
import ExpandableDiv from './custom/ExpandableDiv'
import Select from './custom/InputDropdown'
import MultiOptionButton from './custom/MultiOptionButton/multi-option-button'
import Pills from './custom/Pills/pills'
import ToggleTag from './custom/ToggleTag/toggle-tag'
// Dashboard
import RadioButtonGroup from './dashboard/radio-button-group'
import DataBlock from './data-block'
import DatePicker from './date-picker'
import DayOfWeekPicker from './day-of-week-picker'
import Dropdown from './dropdown'
import DropdownInput from './dropdown-input'
import EditButton from './edit-button'
import ErrorBoundary from './error-boundary'
import FileInput from './file-input'
import FormattedCosts from './formatted-costs'
import FormattedCount from './formatted-count'
import FormattedDistance from './formatted-distance'
import FormattedDuration from './formatted-duration'
import FormattedPhone from './formatted-phone'
import FormattedTemperature from './formatted-temperature'
import FormattedTime from './formatted-time'
import FormattedUserDate from './formatted-user-date'
import FormattedUserTime from './formatted-user-time'
import FormattedVolume from './formatted-volume'
import IconButton from './icon-button'
import ImageSelector from './image-selector'
import InfoBar from './info-bar'
import InputDropdown from './input-dropdown'
import IntlGlobalProvider from './intl-global-provider'
import Legend from './legend'
import LoadingBarAnimation from './loading-bar-animation'
import FormattedLatLng from './map/google/formatted-latlng'
import GeoFuellingStationMarker from './map/google/layers/geo-fuelling-station-marker'
// Google Map components
import geofenceLayer from './map/google/layers/geofence-layer'
import GPSCoordinates from './map/google/layers/gps-coordinates'
import { renderLandmarkMarker } from './map/google/layers/landmark-marker'
import PieCluster from './map/google/layers/pie-cluster'
import RenderIfInBounds from './map/google/layers/render-if-in-bounds'
import SensorMarker, { TableSensorMarker } from './map/google/layers/sensor-marker'
import VehicleCluster from './map/google/layers/vehicle-cluster'
import vehicleComponents from './map/google/layers/vehicle-components'
import VehicleMarker from './map/google/layers/vehicle-marker'
// Share Map components
import ContextMenu from './map/shared/context-menu'
import { EventMarker, EventMarkerInfo } from './map/shared/event-marker'
import Hover from './map/shared/hover'
import MapViewTray from './map/shared/map-view-tray'
import SectionSubHeader from './mifleet/section-sub-header'
import MinimizableSearchBar from './minimizable-search-bar'
import Modal, { DecisionModal } from './modal'
import OldTimelineBar from './old-timeline-bar'
import PasswordInput from './password-input'
import PersonInfo from './person-info'
import PhoneInput from './phone-input'
import ProgressBar from './progress-bar'
import RadioInput from './radio-input'
import RealTimePicker from './real-time-picker'
import InfoRightPanel from './right-panel'
import RotatedIconButton from './rotated-icon-button'
import SearchBar from './search-bar'
import SectionHeader from './section-header'
import MultiSelect from './selects/multi-select'
import Spacer from './spacer'
import Spinner from './spinner'
import StarRating from './star-rating'
import Stats from './stats'
import StatusBadge from './status-badge'
import StatusBar from './StatusBar'
import Table from './table'
import Tabs from './tabs'
import TextInput from './text-input'
import ThreeDots from './three-dots'
import TimeInput from './time-input'
import ToggleButton from './toggle-button'
import ToggleDropdown from './toggle-dropdown'
import Tooltip, { withTooltip } from './tooltip'
import UserInfo from './user-info'
import vehicleIcons, { vehicleMarkerBorderIcons } from './vehicle-icons'
import VerticalNav from './vertical-nav'

export { default as FormattedNumber } from './FormattedNumber'

export {
  AdminHeader,
  AdvancedTable,
  AlertBar,
  AlertButton,
  AlertsBadge,
  Breadcrumbs,
  Button,
  Carousel,
  Checkbox,
  ClassWrapper,
  CollapsedList,
  colorOptions,
  Container,
  ContainerHeader,
  copyToClipboard,
  DataBlock,
  DatePicker,
  DayOfWeekPicker,
  DecisionModal,
  Dropdown,
  EditButton,
  ErrorBoundary,
  FileInput,
  FormattedCount,
  FormattedCosts,
  FormattedDistance,
  FormattedDuration,
  FormattedLatLng,
  FormattedPhone,
  FormattedUserTime,
  FormattedTime,
  FormattedUserDate,
  FormattedTemperature,
  FormattedVolume,
  IconButton,
  InfoBar,
  InputDropdown,
  IntlGlobalProvider,
  ImageSelector,
  Legend,
  MiFleetDatePicker,
  Modal,
  MultiOptionButton,
  PasswordInput,
  Pills,
  RadioInput,
  RotatedIconButton,
  SearchBar,
  MinimizableSearchBar,
  SectionHeader,
  TableSensorMarker,
  Spacer,
  Spinner,
  StarRating,
  Stats,
  StatusBadge,
  StatusBar,
  Table,
  Tabs,
  TextInput,
  OldTimelineBar,
  TimeInput,
  RealTimePicker,
  ToggleDropdown,
  ToggleTag,
  Tooltip,
  withTooltip,
  UserInfo,
  PersonInfo,
  vehicleIcons,
  vehicleMarkerBorderIcons,
  VerticalNav,
  SectionSubHeader,
  Select,
  Alert,
  LoadingBarAnimation,
  MultiSelect,
  PhoneInput,
  ToggleButton,
  InfoRightPanel,
  DropdownInput,
  ProgressBar,
  ThreeDots,
  ExpandableDiv,
  ContextMenu,
  EventMarker,
  EventMarkerInfo,
  Hover,
  MapViewTray,
  geofenceLayer,
  GPSCoordinates,
  renderLandmarkMarker,
  GeoFuellingStationMarker,
  PieCluster,
  RenderIfInBounds,
  SensorMarker,
  vehicleComponents,
  VehicleMarker,
  VehicleCluster,
  useMifleetFormattedNumber,
  // End of Map components
  // Dashboard
  RadioButtonGroup,
}

export * from './dropdown'
export * from './form-visible-ifs'
export * from './formatted-distance'
export * from './formatted-temperature'
export * from './input-dropdown'
export * from './selects/creatable-select'
export * from './ctIntl'
export * from './ctToast'
export * from './ImageWithFallback'
