.TimelineSlider {
  align-items: center;
  display: flex;
  height: 100%;
  padding: 24px 8px;

  &-left {
    color: $gray60;
    flex: 0;
    font-size: 40px;
  }

  &-center {
    flex: 1;
    margin: 5px 20px;
    position: relative;
  }

  &-right {
    flex: 0;
    margin: 5px;

    .speedButton {
      border: none;
      box-shadow: inset 0 3px 3px 0 rgba(0, 0, 0, 0.15);
    }

    &-icon {
      fill: var(--styleButtonActionTextColourActive);
      height: 11px;
      width: 11px;
    }
  }

  &-timeline {
    background-color: $gray30;
    border-radius: 30px;
    height: 10px;
    padding: 0;
    position: relative;
  }

  &-timelinePlay {
    cursor: pointer;
  }

  &-timelineEvent {
    position: absolute;
    height: 100%;
    z-index: 1;
    border-left: none;
    border-right: none;

    &.idling {
      background-color: $idling;
    }

    &.driving {
      background-color: $driving;
    }

    &.alert {
      background-color: $violation;
      z-index: 10;
    }
  }

  &-alert {
    background: $violation;
    height: 10px;
    position: absolute;
    width: 3px;
    z-index: 90;
  }
}
