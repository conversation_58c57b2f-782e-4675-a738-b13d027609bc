import { shape } from 'prop-types'
import { createAction } from '@reduxjs/toolkit'
import { mapKeys, isEmpty, kebabCase, uniqBy, type Dictionary } from 'lodash'
import {
  CLOSE_DETAILS_PANEL,
  RECEIVE_DRIVER_ELD_EVENTS,
  uniqListByColumn,
} from 'duxs/shared'

import {
  toImmutable,
  reconciliateGroupItemIds,
  makeHash,
} from 'src/util-functions/functional-utils'
import {
  checkForValidCoords,
  distanceBetweenLatLngInKilometers,
} from 'src/util-functions/map-utils'

import { ctIntl } from 'src/util-components/ctIntl'
import type { AppState } from 'src/root-reducer'
import type { ActionStatus, FixMeAny, TripsDownloadFileExtension } from 'src/types'
import type { FetchVehicleSummaryTripsUI } from 'src/api/vehicles/types'
import { createStatefulAction } from './utils'
import type {
  FetchDashboardVehiclesResolved,
  FetchVehicleSensorsResolved,
  FetchVehiclesResolved,
  ParsedImmobiliseVehicleData,
} from 'api/vehicles'
import type { VehicleGroupId, VehicleId, VehicleType, DriverName } from 'api/types'
import { createSelectorWithStrictMode } from 'src/redux-utils'
import { batchReduxStateUpdatesFromSaga } from 'src/sagas/actions'
import type { ExcludeStrict } from 'src/types/utils'
import type { Except, ReadonlyDeep } from 'type-fest'
import { getVehicleIdDriversMap } from './drivers'
import { getHardwareTypes } from './vehicle-sensitive-selectors'

// Constants
export const METERS_PER_TA = 550

// Actions
const prefix = 'vehicles/'

export const apiFetchImmobiliseVehicleData = createStatefulAction<void>(
  'vehicles/apiFetchImmobiliseVehicleData',
)

export const receiveVehicleSensors = createAction<{
  sensors: FetchVehicleSensorsResolved
}>('timeline/receiveVehicleSensors')

export const receiveVehiclesAndFocusedEvents = createAction<{
  vehicles: FetchVehiclesResolved['vehicles']
  groups: FetchVehiclesResolved['groups']
  hardwareTypes: FetchVehiclesResolved['hardwareTypes']
  vehicleTypes: FetchVehiclesResolved['vehicleTypes']
}>('global/receiveVehiclesAndFocusedEvents')

export const mountVehicleDetailsModal = createAction<{
  vehicleId: VehicleId
}>(prefix + 'mountVehicleDetailsModal')

export const unmountVehicleDetailsModal = createAction<void>(
  prefix + 'unmountVehicleDetailsModal',
)

// General
export const CANCEL_VEHICLE_POSITION_POLLING = 'CANCEL_VEHICLE_POSITION_POLLING'
export const RECEIVE_SHARED_VEHICLE_DATA = 'RECEIVE_SHARED_VEHICLE_DATA'
export const FETCH_VEHICLE_GEOFENCES = 'FETCH_VEHICLE_GEOFENCES'
export const RECEIVE_VEHICLE_GEOFENCES = 'RECEIVE_VEHICLE_GEOFENCES'
export const FETCH_VEHICLE_LIST = 'FETCH_VEHICLE_LIST'

// Map
export const DOWNLOAD_VEHICLE_TRIP_SPECIAL = 'DOWNLOAD_VEHICLE_TRIP_SPECIAL'
export const SET_TRIP_FLAG = 'SET_TRIP_FLAG'
export const UPDATE_VEHICLES_POSITION = 'UPDATE_VEHICLES_POSITION'
export const POLL_VEHICLES_POSITION = 'POLL_VEHICLES_POSITION'
export const FETCH_DASHBOARD_VEHICLE_LIST = 'FETCH_DASHBOARD_VEHICLE_LIST'
export const CLEAR_DASHBOARD_VEHICLE_LIST = 'CLEAR_DASHBOARD_VEHICLE_LIST'
export const TRY_REFETCH_VEHICLES_POSITIONS = 'TRY_REFETCH_VEHICLES_POSITIONS'

// Details Panel
export const fetchVehicleSummaryTripsUI =
  createStatefulAction<FetchVehicleSummaryTripsUI.Return>(
    prefix + 'fetchVehicleSummaryTripsUI',
  )

export const receiveDashboardVehicleList = createAction<FetchDashboardVehiclesResolved>(
  prefix + 'receiveDashboardVehicleList',
)

export type ReduxVehicles = ReadonlyArray<
  FetchVehiclesResolved['vehicles'][number] & {
    // WORKAROUND The following fields are __apparently__ not present in the API response but in a transition period are useful to make TS happy in specific situations
    altClassName?: undefined
    eventTimestamp?: undefined
    fuelLevel?: undefined
    width?: undefined
    positionType?: undefined
    radius?: undefined
    value?: undefined
    driverName?: DriverName
    adBlueLevel?: undefined
    // ---------

    currentGeofence?: ''
    __webSocketEventTsUnix?: number
  }
>

export type RealTimeVehicleUpdateObject = Pick<
  ReduxVehicles[number],
  'latitude' | 'longitude' | 'bearing' | 'speed' | '__webSocketEventTsUnix'
>

export type ReduxVehicleGroups = FetchVehiclesResolved['groups']

// Detail Pages
export const FETCH_SENSOR_RESOURCES = 'FETCH_SENSOR_RESOURCES'
export const RECEIVE_SENSOR_RESOURCES = 'RECEIVE_SENSOR_RESOURCES'
export const FETCH_VEHICLE_DBITS = 'FETCH_VEHICLE_DBITS'
export const RECEIVE_VEHICLE_DBITS = 'RECEIVE_VEHICLE_DBITS'
export const FETCH_DBIT_RESOURCES = 'FETCH_DBIT_RESOURCES'
export const RECEIVE_DBIT_RESOURCES = 'RECEIVE_DBIT_RESOURCES'

// Immobilise Vehicle
export const FETCH_IMMOBILISE_VEHICLE_ALLOWED = 'FETCH_IMMOBILISE_VEHICLE_ALLOWED'
export const IMMOBILISE_OR_ACTIVATE_VEHICLE = 'IMMOBILISE_OR_ACTIVATE_VEHICLE'
export const SUBMIT_VALIDATION_CODE = 'SUBMIT_VALIDATION_CODE'
export const TRIGGER_IMMOBILISE_VEHICLE_DATA_POLL =
  'TRIGGER_IMMOBILISE_VEHICLE_DATA_POLL'
export const fetchImmobiliseVehicleData = createAction(
  prefix + 'fetchImmobiliseVehicleData',
)

export const updateVehicleLink = createAction<{ vehicleId: VehicleId; expireTs: Date }>(
  prefix + 'updateVehicleLink',
)

// Group Pages
export const UPDATE_VEHICLE_GROUP = 'UPDATE_VEHICLE_GROUP'
export const DELETE_VEHICLE_GROUP = 'DELETE_VEHICLE_GROUP'

// Sensors
export const FETCH_VEHICLE_SENSORS = 'FETCH_VEHICLE_SENSORS'

// Shared vehicle
export const FETCH_SHARED_VEHICLE = 'FETCH_SHARED_VEHICLE'
export const FETCH_NEW_SHARED_VEHICLE_POSITION = 'FETCH_NEW_SHARED_VEHICLE_POSITION'
export const RECEIVE_NEW_SHARED_VEHICLE_POSITION = 'RECEIVE_NEW_SHARED_VEHICLE_POSITION'

// Raw Data
export const FETCH_VEHICLE_RAW_DATA = 'FETCH_VEHICLE_RAW_DATA'
export const RECEIVE_VEHICLE_RAW_DATA = 'RECEIVE_VEHICLE_RAW_DATA'

type DefinedParsedImmobiliseVehicleData = ExcludeStrict<
  ParsedImmobiliseVehicleData,
  false
>

type State = ReadonlyDeep<{
  wasFirstVehiclesListRequestFetched: boolean
  fetchImmobiliseVehicleDataApiStatus: ActionStatus
  immobiliseOrActivateVehicleApiStatus: ActionStatus
  selectedVehicleId: VehicleId | null
  vehicles: ReduxVehicles
  vehiclesLoading: boolean
  vehicleTypes: Array<{
    id: VehicleType
    description: string
  }>
  hardwareTypes: Array<string>
  hasLoaded: boolean
  hasLoadedDetails: boolean
  immobiliseVehicleMetaData:
    | {
        data: Except<DefinedParsedImmobiliseVehicleData, 'keyStatus'> & {
          keyStatus:
            | DefinedParsedImmobiliseVehicleData['keyStatus']
            | 'password_validation'
            | 'password_validating'
            | 'password_invalid'
            | 'otp_validating'
            | 'submittedExpiredCode'
        }
      }
    | undefined
    | false

  dbits: Array<FixMeAny>
  cachedVehicles: Record<string, any> | undefined
  groups: ReduxVehicleGroups
  trips: Array<FixMeAny>
  tripsSummaryPanelUI: FetchVehicleSummaryTripsUI.Return
  sensors: Array<FixMeAny>
  sensorResources: Record<string, any>
  dbitResources: Record<string, any>
  vehicleTokens: Record<VehicleId, { expireTs: Date; token: string } | undefined>
  isUpdatingVehicleLink: boolean
  isFetchingSharedVehicle: boolean
  isLoadingSummaryTrips: boolean
  dashboardVehicles: FetchDashboardVehiclesResolved['dashboardVehicles']
  dashboardVehiclesLoaded: boolean
  sensorsByName: FetchDashboardVehiclesResolved['sensorsByName']
  otpVehicleId: FixMeAny
  vehicleRawData: Array<FixMeAny>
  isFetchingRawData: boolean
  sharedVehicle: Record<string, any> | undefined

  odometer: Array<FixMeAny>
  isCreatingVehicleGroup: boolean
}>

export type VehiclesReduxState = State

// Reducer
const initialState: State = {
  wasFirstVehiclesListRequestFetched: false,
  fetchImmobiliseVehicleDataApiStatus: 'idle',
  immobiliseOrActivateVehicleApiStatus: 'idle',
  selectedVehicleId: null,
  vehicles: [],
  vehiclesLoading: false,
  vehicleTypes: [],
  hardwareTypes: [],
  hasLoaded: false,
  hasLoadedDetails: false,
  immobiliseVehicleMetaData: undefined,
  sharedVehicle: undefined,
  dbits: [],
  cachedVehicles: undefined,
  groups: [],
  trips: [],
  tripsSummaryPanelUI: [], // Used to display the vehicle timelines summary in the details panel
  sensors: [],
  sensorResources: {
    sensorTypes: [],
    alarmPriorities: [],
  },
  dbitResources: {
    sensorTypes: [],
    dbitTypes: [],
  },
  vehicleTokens: {},
  isUpdatingVehicleLink: false,
  isFetchingSharedVehicle: false,
  isLoadingSummaryTrips: false,
  dashboardVehicles: [],
  dashboardVehiclesLoaded: false,
  sensorsByName: [],
  otpVehicleId: undefined,
  // Raw Data
  vehicleRawData: [],
  isFetchingRawData: false,

  odometer: [],
  isCreatingVehicleGroup: false,
}

export default function reducer(state = initialState, action: FixMeAny): State {
  if (receiveVehicleSensors.match(action)) {
    return { ...state, sensors: action.payload.sensors }
  } else if (fetchVehicleSummaryTripsUI.processing.match(action)) {
    return { ...state, isLoadingSummaryTrips: true }
  } else if (fetchVehicleSummaryTripsUI.succeeded.match(action)) {
    return {
      ...state,
      tripsSummaryPanelUI: action.payload,
      isLoadingSummaryTrips: false,
    }
  } else if (fetchVehicleSummaryTripsUI.failed.match(action)) {
    return { ...state, isLoadingSummaryTrips: false }
  } else if (receiveVehiclesAndFocusedEvents.match(action)) {
    const vehiclesCurrentlyInState = state.vehicles
    const newVehiclesFromPayload = action.payload.vehicles
    const newVehiclesWithLastValidPosition = ((): ReduxVehicles => {
      // We want the new vehicles from payload to be returned with the last valid position and currentGeofence from the state
      if (vehiclesCurrentlyInState.length > 0) {
        const vehiclesFromStateByVehicleId = new Map<
          string,
          (typeof vehiclesCurrentlyInState)[number]
        >()

        for (const vehicle of vehiclesCurrentlyInState) {
          vehiclesFromStateByVehicleId.set(vehicle.id, vehicle)
        }

        return newVehiclesFromPayload.map(
          (vehicleFromPayload): ReduxVehicles[number] => {
            const vehicleFromState = vehiclesFromStateByVehicleId.get(
              vehicleFromPayload.id,
            )

            if (vehicleFromState !== undefined) {
              const {
                latitude: previousLatitudeFromState,
                longitude: previousLongitudeFromState,
              } = vehicleFromState

              const coordsMeta = checkForValidCoords(vehicleFromPayload)

              const currentStateToKeep: RealTimeVehicleUpdateObject | null =
                // If the current state event has data from websockets, don't override it. This prevents the vehicle from jumping to a possible previous event/position on the map.
                // Let the web sockets be the source of truth for the vehicle's position if previous events have already been fetched from there.
                vehicleFromState.__webSocketEventTsUnix
                  ? {
                      bearing: vehicleFromState.bearing,
                      speed: vehicleFromState.speed,
                      latitude: vehicleFromState.latitude,
                      longitude: vehicleFromState.longitude,
                      __webSocketEventTsUnix: vehicleFromState.__webSocketEventTsUnix,
                    }
                  : null

              // Return vehicle from payload with last valid coords and currentGeofence from state
              return {
                // Mark event as not coming from websockets. But can be overridden by the currentStateToKeep object
                __webSocketEventTsUnix: undefined,
                ...vehicleFromState,
                ...vehicleFromPayload,
                ...(coordsMeta.hasValidCoords
                  ? { latitude: coordsMeta.lat, longitude: coordsMeta.lng }
                  : {
                      latitude: previousLatitudeFromState,
                      longitude: previousLongitudeFromState,
                    }),
                ...currentStateToKeep,
              }
            }

            // Fallback to return vehicle from payload
            return vehicleFromPayload
          },
        )
      }

      return newVehiclesFromPayload
    })()

    return {
      ...state,
      vehicles: newVehiclesWithLastValidPosition,
      vehiclesLoading: false,
      vehicleTypes: action.payload.vehicleTypes,
      hardwareTypes: action.payload.hardwareTypes,
      cachedVehicles:
        state.cachedVehicles || mapKeys(action.payload.vehicles, (v) => v.id),
      groups: action.payload.groups,
      hasLoaded: true,
    }
  } else if (apiFetchImmobiliseVehicleData.processing.match(action)) {
    return { ...state, fetchImmobiliseVehicleDataApiStatus: 'processing' }
  } else if (apiFetchImmobiliseVehicleData.succeeded.match(action)) {
    return { ...state, fetchImmobiliseVehicleDataApiStatus: 'succeeded' }
  } else if (apiFetchImmobiliseVehicleData.failed.match(action)) {
    return { ...state, fetchImmobiliseVehicleDataApiStatus: 'failed' }
  } else if (mountVehicleDetailsModal.match(action)) {
    const { vehicleId } = action.payload
    return {
      ...state,
      selectedVehicleId: vehicleId,
    }
  } else if (unmountVehicleDetailsModal.match(action)) {
    return {
      ...state,
      selectedVehicleId: null,
    }
  }
  if (receiveDashboardVehicleList.match(action)) {
    return {
      ...state,
      dashboardVehicles: action.payload.dashboardVehicles,
      sensorsByName: action.payload.sensorsByName,
      dashboardVehiclesLoaded: true,
    }
  }
  if (batchReduxStateUpdatesFromSaga.match(action)) {
    if (action.payload.vehiclesState === undefined) {
      return state
    }
    return typeof action.payload.vehiclesState === 'function'
      ? action.payload.vehiclesState(state)
      : { ...state, ...action.payload.vehiclesState }
  }
  if (updateVehicleLink.match(action)) {
    return {
      ...state,
      vehicleTokens: {
        ...state.vehicleTokens,
        [action.payload.vehicleId]: undefined,
      },
      isUpdatingVehicleLink: true,
    }
  }

  switch (action.type) {
    case FETCH_DASHBOARD_VEHICLE_LIST: {
      return {
        ...state,
        dashboardVehiclesLoaded: false,
      }
    }
    case CLEAR_DASHBOARD_VEHICLE_LIST: {
      return { ...state, dashboardVehicles: [], sensorsByName: [] }
    }
    case UPDATE_VEHICLES_POSITION: {
      return {
        ...state,
        vehicles: state.vehicles.map((v) =>
          v.id in action.payload.positions
            ? { ...v, ...action.payload.positions[v.id] }
            : v,
        ),
      }
    }
    // Clear cache used to draw scrubbable timeline
    case CLOSE_DETAILS_PANEL: {
      return {
        ...state,
        trips: [],
      }
    }
    case RECEIVE_VEHICLE_GEOFENCES: {
      return {
        ...state,
        vehicles: state.vehicles.map((v): ReduxVehicles[number] => {
          if (v.id in action.payload.currentGeofences) {
            return {
              ...v,
              currentGeofence: action.payload.currentGeofences[v.id] ?? '',
            }
          }

          return { ...v, currentGeofence: '' }
        }),
      }
    }
    case RECEIVE_SENSOR_RESOURCES: {
      return {
        ...state,
        sensorResources: action.payload.sensorResources,
      }
    }
    case RECEIVE_VEHICLE_DBITS: {
      return {
        ...state,
        dbits: action.payload.dbits,
      }
    }
    case RECEIVE_DBIT_RESOURCES: {
      return {
        ...state,
        dbitResources: action.payload.dbitResources,
      }
    }

    case FETCH_SHARED_VEHICLE: {
      return {
        ...state,
        isFetchingSharedVehicle: true,
      }
    }

    case RECEIVE_SHARED_VEHICLE_DATA: {
      return {
        ...state,
        sharedVehicle: { ...state.sharedVehicle, ...action.payload.vehicle },
        isFetchingSharedVehicle: false,
      }
    }

    case RECEIVE_NEW_SHARED_VEHICLE_POSITION: {
      return {
        ...state,
        sharedVehicle: {
          ...state.sharedVehicle,
          ...action.payload.position,
        },
      }
    }

    case RECEIVE_DRIVER_ELD_EVENTS: {
      return {
        ...state,
        isLoadingSummaryTrips: false,
      }
    }

    // Raw Data
    case FETCH_VEHICLE_RAW_DATA: {
      return {
        ...state,
        isFetchingRawData: true,
      }
    }
    case RECEIVE_VEHICLE_RAW_DATA: {
      return {
        ...state,
        vehicleRawData: action.payload.data,
        isFetchingRawData: false,
      }
    }

    case FETCH_VEHICLE_LIST: {
      return {
        ...state,
        vehiclesLoading: true,
      }
    }
    default: {
      return state
    }
  }
}

// Action Creators

export function downloadVehicleTripSpecial({
  type,
  id,
  startTime,
  endTime,
  zeroKmTrips = false,
  flaggedTripsOnly = false,
}: {
  type: TripsDownloadFileExtension
  id: string
  startTime: Date
  endTime: Date
  zeroKmTrips?: boolean
  flaggedTripsOnly?: boolean
}) {
  return {
    type: DOWNLOAD_VEHICLE_TRIP_SPECIAL,
    payload: { type, id, startTime, endTime, zeroKmTrips, flaggedTripsOnly },
  }
}

export function setTripFlag(
  vehicleId: VehicleId,
  startTime: Date,
  isFlagged: boolean,
  timelinePayload: {
    startTime: Date
    endTime: Date
    isSingleTrip: boolean
  },
) {
  return {
    type: SET_TRIP_FLAG,
    payload: { vehicleId, startTime, isFlagged, timelinePayload },
  }
}

export function fetchDashboardVehicleList() {
  return {
    type: FETCH_DASHBOARD_VEHICLE_LIST,
  }
}

export function clearDashboardVehicleList() {
  return {
    type: CLEAR_DASHBOARD_VEHICLE_LIST,
  }
}

// List
export function fetchVehicleGeofences(vehicleIds: Array<string>) {
  return {
    type: FETCH_VEHICLE_GEOFENCES,
    payload: { vehicleIds },
  }
}

export function fetchVehicleList() {
  return {
    type: FETCH_VEHICLE_LIST,
  }
}

export function fetchSensorResources() {
  return {
    type: FETCH_SENSOR_RESOURCES,
  }
}

export function fetchDbitResources() {
  return {
    type: FETCH_DBIT_RESOURCES,
  }
}

export function fetchVehicleDbits(vehicleId: FixMeAny) {
  return {
    type: FETCH_VEHICLE_DBITS,
    payload: { vehicleId },
  }
}

// Groups
export function updateVehicleGroup(group: FixMeAny, selectedIds: FixMeAny) {
  return {
    type: UPDATE_VEHICLE_GROUP,
    payload: { group, selectedIds },
  }
}

export function deleteVehicleGroup(groupId: FixMeAny, groupName: FixMeAny) {
  return {
    type: DELETE_VEHICLE_GROUP,
    payload: { groupId, groupName },
  }
}

export function fetchVehicleSensors(vehicleId: FixMeAny) {
  return {
    type: FETCH_VEHICLE_SENSORS,
    payload: { vehicleId },
  }
}

// Shared
export function fetchSharedVehicle(vehicleId: FixMeAny) {
  return {
    type: FETCH_SHARED_VEHICLE,
    payload: {
      vehicleId,
    },
  }
}

export function fetchSharedVehiclePosition(vehicleId: VehicleId) {
  return {
    type: FETCH_NEW_SHARED_VEHICLE_POSITION,
    payload: {
      vehicleId,
    },
  }
}

export function fetchImmobiliseVehicleAllowed(vehicleId: VehicleId) {
  return {
    type: FETCH_IMMOBILISE_VEHICLE_ALLOWED,
    payload: {
      vehicleId,
    },
  }
}

export function toggleImmobiliseOrActivateVehicle(vehicleId: VehicleId) {
  return {
    type: IMMOBILISE_OR_ACTIVATE_VEHICLE,
    payload: {
      vehicleId,
    },
  }
}

export function submitValidationCode({
  vehicleId,
  value,
}: {
  vehicleId: VehicleId
  value: { type: 'password'; password: string } | { type: 'otp'; code: string }
}) {
  return {
    type: SUBMIT_VALIDATION_CODE,
    payload: {
      vehicleId,
      value,
    },
  }
}

// Raw Data
export function fetchVehicleRawData(vehicleId: FixMeAny, selectedDate: FixMeAny) {
  return {
    type: FETCH_VEHICLE_RAW_DATA,
    payload: { vehicleId, date: selectedDate },
  }
}

// Selectors
export const getSelectedVehicleId = (state: AppState) =>
  state.vehicles.selectedVehicleId
export const getSelectedVehicle = (state: AppState) => {
  const vehicleId = getSelectedVehicleId(state)
  return vehicleId ? getVehicle(state, vehicleId) : undefined
}

export const getDashboardVehicles = (state: AppState) =>
  state.vehicles.dashboardVehicles ?? initialState.dashboardVehicles
export const getDashboardVehiclesLoaded = (state: AppState) =>
  state.vehicles.dashboardVehiclesLoaded
export const getDashboardSensorsByName = (state: AppState) =>
  state.vehicles.sensorsByName ?? initialState.sensorsByName

export const getHasLoaded = (state: AppState) => state.vehicles.hasLoaded
export const getVehicles = (state: AppState) => state.vehicles.vehicles
export const getVehiclesLoading = (state: AppState) => state.vehicles.vehiclesLoading

export const getVehiclesById = createSelectorWithStrictMode(getVehicles, (vehicles) => {
  const vehiclesById = new Map<VehicleId, (typeof vehicles)[number]>()
  for (const vehicle of vehicles) {
    vehiclesById.set(vehicle.id, vehicle)
  }
  return vehiclesById
})
export const getVehiclesIds = createSelectorWithStrictMode(getVehicles, (vehicles) =>
  vehicles.map((v) => v.id),
)
export const getWasFirstVehiclesListRequestFetched = (state: AppState) =>
  state.vehicles.wasFirstVehiclesListRequestFetched

export const getVehicleTypes = (state: AppState) => state.vehicles.vehicleTypes

export const getVehicleTypesById = createSelectorWithStrictMode(
  getVehicleTypes,
  (vehicleTypes) =>
    vehicleTypes.reduce(
      (acc, vehicleType) => {
        acc[vehicleType.id] = vehicleType
        return acc
      },
      {} as Record<VehicleType, (typeof vehicleTypes)[number] | undefined>,
    ),
)

export const getVehicleTypeOptions = createSelectorWithStrictMode(
  getVehicleTypes,
  (vehicleTypes) =>
    vehicleTypes.map((type) => ({
      label: ctIntl.formatMessage({ id: type.description }),
      name: ctIntl.formatMessage({ id: type.description }),
      value: type.id,
    })),
)

export const getCachedVehicles = (state: AppState) =>
  (state.vehicles.cachedVehicles as Dictionary<
    FetchVehiclesResolved['vehicles'][number]
  >) || {}
export const getCachedVehiclesOptions = createSelectorWithStrictMode(
  getCachedVehicles,
  (vehicles) =>
    Object.keys(vehicles)
      .map((key) => ({
        name: vehicles[key].registration,
        label: vehicles[key].registration,
        value: key,
      }))
      .sort((a, b) => a.name.localeCompare(b.name)),
)
export const getVehicleSensors = (state: AppState) => state.vehicles.sensors
/**
 *  Should generally be avoided as it requires you to use it in places where you know you have vehicles.
 *  For instance, if you used this selector in ELD map, it would give you the selected driver ID, not the vehicle ID.
 */
export const getFocusedItemIdAsVehicleId = (state: AppState) => state.map.focusedItemId

// Immobilise Vehicle
export const getImmobiliseVehicleMetaData = (state: AppState) =>
  state.vehicles.immobiliseVehicleMetaData

// Vehicle Odometer
export const getVehicleOdometer = (state: AppState) => state.vehicles.odometer

export const getVehicleGroups = (state: AppState) => state.vehicles.groups ?? []

export const getVehicleGroupsById = createSelectorWithStrictMode(
  getVehicleGroups,
  (groups) => {
    const groupsById = new Map<VehicleGroupId, (typeof groups)[number]>()
    for (const group of groups) {
      groupsById.set(group.id, group)
    }
    return groupsById
  },
)

export const getVehicleGroupsOptions = createSelectorWithStrictMode(
  getVehicleGroups,
  (groups) => groups.map((item) => ({ name: item.name, value: item.name })),
)

export const getCachedVehiclesByTerminal = createSelectorWithStrictMode(
  getCachedVehicles,
  (vehicles) => mapKeys(vehicles, (v) => v.terminalSerial),
)

export const getActivatedVehicles = createSelectorWithStrictMode(
  getVehicles,
  (vehicles) => vehicles.filter((v) => v.active),
)

type DriverFromList = AppState['drivers']['drivers'][number]
export const getVehicleCurrentDriver = (
  vehicleId: VehicleId,
  vehicleIdDriversMap: ReturnType<typeof getVehicleIdDriversMap>,
): DriverFromList | null => {
  const possibleDrivers = vehicleIdDriversMap.get(vehicleId)
  if (!possibleDrivers || possibleDrivers.length === 0) {
    return null
  }
  return possibleDrivers.findLast((d) => d.seenToday) || possibleDrivers[0]
}

export const getActiveVehiclesWithDrivers = createSelectorWithStrictMode(
  getActivatedVehicles,
  (state: AppState) => getVehicleIdDriversMap(state), // Circular dependency guard
  (vehicles, vehicleIdDriversMap) =>
    toImmutable(
      vehicles.map((v) => ({
        ...v,
        __entityType__: 'VEHICLE' as const,
        driver: getVehicleCurrentDriver(v.id, vehicleIdDriversMap),
      })),
    ),
)

export const getAvailableVehiclesToShowInMapWithDrivers = createSelectorWithStrictMode(
  getVehicles,
  (state: AppState) => getVehicleIdDriversMap(state), // Circular dependency guard
  (vehicles, vehicleIdDriversMap) => {
    const availableVehicles: Array<
      (typeof vehicles)[number] & {
        driver: ReturnType<typeof getVehicleCurrentDriver>
      }
    > = []

    for (const vehicle of vehicles) {
      if (!vehicle.active) {
        continue
      }
      if (checkForValidCoords(vehicle).hasValidCoords && !vehicle.privacyModeEnabled) {
        availableVehicles.push({
          ...vehicle,
          driver: getVehicleCurrentDriver(vehicle.id, vehicleIdDriversMap),
        })
      }
    }

    return toImmutable(availableVehicles)
  },
)

export const getOrderedHardwareTypeListWhereFleetIsAlwaysFirst =
  createSelectorWithStrictMode(getHardwareTypes, (hardwareTypes) => {
    // eslint-disable-next-line
    // eslint-disable-next-line unicorn/consistent-function-scoping
    function includes(list: ReadonlyArray<string>, compare: (el: string) => boolean) {
      for (let i = 0, len = list.length; i < len; i++) {
        if (compare(list[i])) return true
      }

      return false
    }

    // NOTE: if hardwareTypes are empty, we should at least display fleet
    if (isEmpty(hardwareTypes)) {
      return ['Fleet']
    }

    return includes(hardwareTypes, (el) => kebabCase(el) === 'fleet')
      ? uniqBy(['Fleet', ...hardwareTypes], kebabCase)
      : [...hardwareTypes] // Spreading here so that reselect dev tools does not complain about the reference being the same
  })

export const getFetchImmobiliseVehicleDataApiStatus = (state: AppState) =>
  state.vehicles.fetchImmobiliseVehicleDataApiStatus

export const getImmobiliseOrActivateVehicleApiStatus = (state: AppState) =>
  state.vehicles.immobiliseOrActivateVehicleApiStatus

export const getVehicle = createSelectorWithStrictMode(
  getVehicles,
  (_: FixMeAny, id: string) => id,
  (vehicles, id) => vehicles.find((v) => v.id === id),
)

export const getVehiclesByTerminal = createSelectorWithStrictMode(
  getVehicles,
  (_: AppState, serials: Array<any>) => serials,
  (vehicles, serials) => {
    const hash = makeHash(serials)
    return vehicles.filter((v) => (v.terminalSerial ? v.terminalSerial in hash : false))
  },
)

// Using getFocusedItemId Would be a circular dependency.
export const getFocusedVehicle = createSelectorWithStrictMode(
  getVehicles,
  getFocusedItemIdAsVehicleId,
  (vehicles, id) => (id && vehicles.find((v) => v.id === id)) || null,
)

export const getTripsSummaryPanelUI = (state: AppState) =>
  state.vehicles.tripsSummaryPanelUI

export const getVehicleGroup = createSelectorWithStrictMode(
  getVehicleGroups,
  getActiveVehiclesWithDrivers,
  (_: AppState, groupId: string) => groupId,
  (groups, vehiclesWithDrivers, groupId) =>
    reconciliateGroupItemIds(
      groups.find((group) => group.id === groupId),
      vehiclesWithDrivers,
    ),
)

export const getVehicleGroupOptions = createSelectorWithStrictMode(
  getVehicleGroups,
  (vehicleGroups) =>
    !isEmpty(vehicleGroups)
      ? vehicleGroups
          .map((item) => ({
            name: item.name,
            label: item.name,
            value: item.id,
            vehicleIds: item.itemIds,
            type: 'group' as const,
          }))
          .filter((group) => group.name)
          .sort((a, b) => a.name.localeCompare(b.name))
      : [],
)

export const getIsCreatingVehicleGroup = (state: AppState) =>
  state.vehicles.isCreatingVehicleGroup

export const getVehicleDistancesFromPoint = createSelectorWithStrictMode(
  (state: AppState) => state.map.focusedPoint,
  getActivatedVehicles,
  (point, vehicles) =>
    point
      ? vehicles
          .map((vehicle) => ({
            ...vehicle,
            distance:
              Math.round(
                distanceBetweenLatLngInKilometers(
                  point.lat,
                  point.lng,
                  vehicle.latitude,
                  vehicle.longitude,
                ) * 4,
              ) / 4,
          }))
          .sort((a, b) => a.distance - b.distance)
      : [],
)

export const getIsUpdatingVehicleLink = (state: AppState) =>
  state.vehicles.isUpdatingVehicleLink

export const getVehicleLinkToken = (state: AppState, vehicleId: VehicleId) => {
  const tokens = state.vehicles.vehicleTokens
  if (tokens[vehicleId] && tokens[vehicleId].expireTs.getTime() > Date.now()) {
    return tokens[vehicleId].token
  }
  return undefined
}

export const getSensorTypes = (state: AppState) =>
  state.vehicles.sensorResources.sensorTypes

export const getAlarmPriorities = (state: AppState) =>
  state.vehicles.sensorResources.alarmPriorities

export const getDbitSensorTypes = (state: AppState) =>
  state.vehicles.dbitResources.sensorTypes

export const getDbitTypes = (state: AppState) => state.vehicles.dbitResources.dbitTypes

export const getVehicleDbits = (state: AppState) => state.vehicles.dbits

export const getSharedVehicleLoadStatus = (state: AppState) =>
  state.vehicles.isFetchingSharedVehicle

export const getSharedVehicle = (state: AppState) => state.vehicles.sharedVehicle

export const getIsLoadingSummaryTrips = (state: AppState) =>
  state.vehicles.isLoadingSummaryTrips

export const getVehiclesOptions = createSelectorWithStrictMode(
  getVehicles,
  (vehicles) => {
    const allOption = {
      name: ctIntl.formatMessage({ id: 'All Vehicles' }),
      value: 'all',
      key: 'all',
    } as const
    const sortedItems = vehicles
      .map((vehicle) => ({
        name: vehicle.registration,
        value: vehicle.id,
        key: vehicle.id,
      }))
      .sort((a, b) => a.name.localeCompare(b.name))

    return [allOption, ...sortedItems] as const
  },
)

export const getUniqVehicleOptions = createSelectorWithStrictMode(
  getActivatedVehicles,
  (vehicles) => uniqListByColumn(vehicles),
)

// Raw Data
export const getVehicleRawData = (state: AppState) => state.vehicles.vehicleRawData
export const isFetchingRawData = (state: AppState) => state.vehicles.isFetchingRawData

// PropTypes
export const vehicleShape = shape({})

export const getVehiclesStateFromSaga = (state: AppState) => state.vehicles
