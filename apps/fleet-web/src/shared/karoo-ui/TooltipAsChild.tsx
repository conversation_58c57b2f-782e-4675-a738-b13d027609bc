import { styled, type SystemStyleObject } from '@karoo-ui/core'
import { match } from 'ts-pattern'

/**
 *
 * __IMPORTANT__: Reason this exists, opposed to just using mui Tooltip component normally, is that it allows for a more precise control of the tooltip's position.
 * Tooltip component does not precisely follow the element's position, specially if it is moving, like the case for a Slider handle moving through time automatically.
 * This uses a css approach that is reliable and precise.
 *
 * Can be used as a child of a component with position: relative/absolute. e.g:
 * ```tsx
 * <Box position="relative">
 *    {tooltipChildren}
 *   <TooltipAsChild placement="top">
 *     <span>Tooltip content</span>
 *   </TooltipAsChild>
 * </Box>
 * ```
 */
export const TooltipAsChild = styled('span')<{
  placement: 'top' | 'bottom'
  offsetFromElement: string
}>(({ theme, placement, offsetFromElement }) =>
  theme.unstable_sx({
    borderRadius: '4px',
    width: 'min-content',

    ...match(placement)
      .returnType<SystemStyleObject>()
      .with('top', () => ({
        position: 'absolute',
        bottom: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        transformOrigin: '50% 100%',
        marginBottom: offsetFromElement,
      }))
      .with('bottom', () => ({
        position: 'absolute',
        top: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        transformOrigin: '50% 0%',
        marginTop: offsetFromElement,
      }))
      .exhaustive(),

    // Styling
    backgroundColor: theme.palette.grey[800],
    color: '#fff',
    px: 1,
    py: 0.5,
    textAlign: 'center',

    // Arrow
    '&::after': (() => {
      const borderY = `6px solid ${theme.palette.grey[800]}`
      const borderX = '6px solid transparent'
      const YOffset = '-5px'
      return {
        content: '""',
        position: 'absolute',
        left: '50%',
        transform: 'translateX(-50%)',
        width: 0,
        height: 0,
        borderLeft: borderX,
        borderRight: borderX,
        ...(placement === 'top'
          ? {
              bottom: YOffset,
              borderTop: borderY,
            }
          : {
              top: YOffset,
              borderBottom: borderY,
            }),
      }
    })(),
  }),
)
