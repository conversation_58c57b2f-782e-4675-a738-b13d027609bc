import { useEffect, useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import {
  Button,
  DataGrid,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridToolbarWithQuickFilter,
  IconButton,
  LinearProgress,
  Stack,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowId,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CopyIcon from '@mui/icons-material/CopyAll'
import DeleteIcon from '@mui/icons-material/DeleteOutlined'
import RefreshIcon from '@mui/icons-material/Refresh'
import { CopyToClipboard } from 'react-copy-to-clipboard'

import { getAuthenticatedUserAsAccountUser } from 'duxs/user'
import { getIsSubUser } from 'duxs/user-sensitive-selectors'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import { DataGridDeleteButtonWithCounter } from 'src/shared/data-grid/DataGridDeleteButtonWithCounter'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

import type { UseApiSettingsUserReturnedData } from './api/queries'
import GenerateApiCredentialModal from './components/GenerateApiCredentialModal'

type DataGridRow = UseApiSettingsUserReturnedData['subusers'][number]

type Props = {
  userList: UseApiSettingsUserReturnedData['subusers']
  isLoading: boolean
  onOpenDeleteApiCredentialModal: (userData: {
    type: 'subuser'
    ids: Array<string>
    onDeleteSuccess: () => void
  }) => void
  onOpenRegenerateApiKeyModal: (userData: { type: 'subuser'; id: string }) => void
}

export default function UserCredentials({
  userList,
  isLoading,
  onOpenDeleteApiCredentialModal,
  onOpenRegenerateApiKeyModal,
}: Props) {
  const isSubUser = useTypedSelector(getIsSubUser)
  const user = useTypedSelector(getAuthenticatedUserAsAccountUser) ?? null
  const columnHelper = useDataGridColumnHelper<DataGridRow>({
    filterMode: 'client',
  })

  const [isGenerateApiCredentialModalOpen, setIsGenerateApiCredentialModalOpen] =
    useState(false)
  const [selectedRowIds, setSelectedRowIds] = useState<ReadonlySet<GridRowId>>(
    new Set(),
  )
  const [passwordCopiedToClipboard, setPasswordCopiedToClipboard] = useState(false)

  const usersWithApiKey = useMemo(
    () => userList.filter((user) => user.apiPassword),
    [userList],
  )

  useEffect(() => {
    if (passwordCopiedToClipboard === true) {
      window.setTimeout(() => setPasswordCopiedToClipboard(false), 3000)
    }
  }, [passwordCopiedToClipboard])

  const columns = useMemo((): Array<GridColDef<DataGridRow>> => {
    const cols: Array<GridColDef<DataGridRow>> = [
      { ...GRID_CHECKBOX_SELECTION_COL_DEF, hideable: false },
      columnHelper.string((_, row) => row.name, {
        field: 'user',
        headerName: ctIntl.formatMessage({
          id: 'User',
        }),
        flex: 1,
      }),
      columnHelper.string(() => user?.account || '', {
        field: 'apiUsername',
        headerName: ctIntl.formatMessage({
          id: 'apiSettings.userCredentials.apiUsername',
        }),
        flex: 1,
      }),
      columnHelper.string((_, row) => row.apiPassword, {
        field: 'apiPassword',
        headerName: ctIntl.formatMessage({
          id: 'apiSettings.userCredentials.apiPassword',
        }),
        flex: 2,
        sortable: false,
        renderCell: ({ row }) => (
          <>
            <Typography>{row.apiPassword}</Typography>

            <CopyToClipboard
              text={row.apiPassword || ''}
              onCopy={() => setPasswordCopiedToClipboard(true)}
            >
              <Tooltip
                title={ctIntl.formatMessage({
                  id: passwordCopiedToClipboard
                    ? 'Copied Successfully'
                    : 'Copy to Clipboard',
                })}
                placement="top"
              >
                <IconButton size="small">
                  <CopyIcon />
                </IconButton>
              </Tooltip>
            </CopyToClipboard>
          </>
        ),
      }),
    ]

    if (!isSubUser) {
      cols.push({
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        align: 'center',
        getActions: ({ row }) => [
          <GridActionsCellItem
            data-testid={`UserCredential-RegenerateApiKeyButton-${row.id}`}
            key="regenerateApiCredentials"
            icon={<RefreshIcon />}
            label={ctIntl.formatMessage({
              id: 'apiSettings.regenerateApiCredentials',
            })}
            onClick={() =>
              onOpenRegenerateApiKeyModal({
                type: 'subuser',
                id: row.id,
              })
            }
          />,

          <GridActionsCellItem
            data-testid={`UserCredential-DeleteApiKeyButton-${row.id}`}
            key="deleteApiPassword"
            icon={<DeleteIcon />}
            label={ctIntl.formatMessage({
              id: 'apiSettings.deleteApiPassword',
            })}
            onClick={() =>
              onOpenDeleteApiCredentialModal({
                type: 'subuser',
                ids: [row.id],
                onDeleteSuccess: () => {
                  setSelectedRowIds([...selectedRowIds].filter((id) => id !== row.id))
                },
              })
            }
          />,
        ],
      })
    }

    return cols
  }, [
    columnHelper,
    isSubUser,
    user.account,
    passwordCopiedToClipboard,
    onOpenRegenerateApiKeyModal,
    onOpenDeleteApiCredentialModal,
    selectedRowIds,
  ])

  return (
    <Stack
      direction="column"
      spacing={1}
    >
      <IntlTypography
        variant="h6"
        msgProps={{
          id: 'apiSettings.userCredentials.header',
        }}
        mb={1}
      />

      {isEmpty(usersWithApiKey) && isSubUser ? (
        <IntlTypography
          msgProps={{
            id: 'apiSettings.userCredentials.noApiKeyMessage',
          }}
        />
      ) : (
        <>
          <IntlTypography
            msgProps={{
              id: isSubUser
                ? 'apiSettings.userCredentials.briefIntroForSubuser'
                : 'apiSettings.userCredentials.briefIntroForAdminUser',
            }}
          />

          {isSubUser ? (
            <UserDataGridWithSavedSettingsOnIDB
              Component={DataGrid}
              dataGridId="UserCredentialList-SubuserView"
              data-testid="UserCredentialList-SubuserView"
              columns={columns}
              rows={usersWithApiKey}
              loading={isLoading}
              hideFooter
              disableRowSelectionOnClick
              slots={{ loadingOverlay: LinearProgress }}
            />
          ) : (
            <UserDataGridWithSavedSettingsOnIDB
              Component={DataGrid}
              dataGridId="UserCredentialList-AdminView"
              data-testid="UserCredentialList-AdminView"
              pagination
              columns={columns}
              rows={usersWithApiKey}
              checkboxSelection
              disableRowSelectionOnClick
              rowSelectionModel={selectedRowIds}
              onRowSelectionModelChange={(selectionModal) =>
                setSelectedRowIds(selectionModal.ids)
              }
              loading={isLoading}
              slots={{
                toolbar: GridToolbarWithQuickFilter,
                loadingOverlay: LinearProgress,
              }}
              slotProps={{
                toolbar: GridToolbarWithQuickFilter.createProps({
                  gridToolbarRightContent: (
                    <>
                      <DataGridDeleteButtonWithCounter
                        count={selectedRowIds.size}
                        disabled={selectedRowIds.size === 0}
                        ButtonProps={{
                          size: 'small',
                          onClick: () =>
                            onOpenDeleteApiCredentialModal({
                              type: 'subuser',
                              ids: [...selectedRowIds].map(String),
                              onDeleteSuccess: () => {
                                setSelectedRowIds(new Set())
                              },
                            }),
                        }}
                      />
                      <Button
                        data-testid="GenerateUserAPICredentialButton"
                        size="small"
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={() => setIsGenerateApiCredentialModalOpen(true)}
                      >
                        {ctIntl.formatMessage({
                          id: 'apiSettings.userCredentials.generateUserCredential',
                        })}
                      </Button>
                    </>
                  ),
                }),
              }}
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: 10,
                    page: 0,
                  },
                },
              }}
              pageSizeOptions={[5, 10, 25]}
            />
          )}

          {isGenerateApiCredentialModalOpen && (
            <GenerateApiCredentialModal
              userList={userList}
              onClose={() => setIsGenerateApiCredentialModalOpen(false)}
            />
          )}
        </>
      )}
    </Stack>
  )
}
