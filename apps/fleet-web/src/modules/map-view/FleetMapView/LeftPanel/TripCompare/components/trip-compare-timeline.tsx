import { useCallback, useEffect } from 'react'
import { DateTime } from 'luxon'

import { getSelectedComparedTrip } from 'duxs/trip-compare'
import TimelineSlider from 'src/modules/map-view/timeline/TimelineSlider'
import { useTypedSelector } from 'src/redux-hooks'

type Props = {
  onChangeMapHeight: (height: string) => void
}

const TripCompareTimeline = ({ onChangeMapHeight }: Props) => {
  const selectedTrip = useTypedSelector(getSelectedComparedTrip)

  const changeMapHeight = useCallback(() => {
    const availableHeight = window.innerHeight
    const timelineHeight = 110

    onChangeMapHeight(100 - Math.round((timelineHeight / availableHeight) * 100) + '%')
  }, [onChangeMapHeight])

  useEffect(() => {
    if (selectedTrip && selectedTrip.tripData) {
      changeMapHeight()
    }

    return () => {
      onChangeMapHeight('100%')
    }
  }, [selectedTrip, changeMapHeight, onChangeMapHeight])

  if (!selectedTrip || !selectedTrip.tripData) return null

  const {
    tripData: { startTime, endTime },
    events,
  } = selectedTrip

  return (
    <div className="TripCompareTimeline">
      <TimelineSlider
        timelineBarUI={selectedTrip.tripData.events}
        timeRange={{
          startDateTime: DateTime.fromMillis(startTime),
          endDateTime: DateTime.fromMillis(endTime),
        }}
        events={events}
      />
    </div>
  )
}

export default TripCompareTimeline
