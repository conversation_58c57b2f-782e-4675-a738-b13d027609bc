import { useEffect, useMemo, useState } from 'react'
import { isEmpty, isEqual, isNil, throttle } from 'lodash'
import { Box, Stack, styled } from '@karoo-ui/core'
import { useDispatch } from 'react-redux'

import useTimelineHardwareTypeQuery from 'api/timeline/useTimelineHardwareTypeQuery'
import {
  changeTimelineTablesActiveTab,
  getSelectedTripStartDate,
  jumpToTime,
} from 'duxs/map'
import { getMapType } from 'duxs/map-timeline'
import type { MapActiveView, MapType } from 'duxs/map-types'
import {
  getTimelineEventsRaw,
  getTimelineSliderDataFilteredByActivity,
  getTimelineTripsSensors,
  tableColumnsVisibility,
} from 'duxs/timeline'
import { getAuthenticatedUser, getHardwareTypeList, savePreferences } from 'duxs/user'
import { getPreferences } from 'duxs/user-sensitive-selectors'
import { usePrevious } from 'src/hooks'
import { pointToPointTableId } from 'src/modules/tables/point-to-point-events'
import { useTypedSelector } from 'src/redux-hooks'
import type { FixMeAny } from 'src/types'
import type { TypedColumns } from 'src/types/extended/react-table'

import { id as vehicleTableId } from '../../tables/vehicle-events'
import type { FocusedVehicle } from '../FleetMapView/DetailsPanel/ui-types'
import type { MapFleetBottomPanelState } from '../MapFleetProvider'
import { MapBottomPanelTripTablesProvider } from './MapBottomPanelTable/MapBottomPanelTripTablesContext'
import ReTimelineChart from './ReTimelineChart'
import ReTimelineHeader from './ReTimelineHeader'
import ReTimelineTables from './ReTimelineTables'
import ReTimelineSlider from './TimelineSlider'

const TIMELINE_MIN_HEIGHT_MAP = {
  vehicles: {
    timeline: 195,
    graph: 380,
    table: 380,
  },
} as const

const TABLE_TIMELINE_HEADER_HEIGHT = 154
const MARGIN_BOTTOM = 16

function resolveHeightPercentage(newHeight: number) {
  const pct = Math.min(Math.round((newHeight / window.innerHeight) * 100), 100)
  return {
    map: 100 - pct + '%',
    timeline: pct + '%',
  }
}

function getIsTimelineTripsView(
  mapType: MapType | '',
): mapType is 'vehicles' | 'fleet' {
  return mapType === 'vehicles' || mapType === 'fleet'
}

type Props = {
  onChangeMapHeight: (height: string) => void
  focusedItem: FocusedVehicle
  bottomPanelState: MapFleetBottomPanelState
}

const ReTimeline = ({ focusedItem, onChangeMapHeight, bottomPanelState }: Props) => {
  const dispatch = useDispatch()
  const user = useTypedSelector(getAuthenticatedUser)
  const username = user.username
  const mapType = useTypedSelector(getMapType)
  const {
    events: timelineSliderEvents,
    timeRange: timelineTimeRange,
    timelineBarUI,
  } = useTypedSelector(getTimelineSliderDataFilteredByActivity)
  const eventsRaw = useTypedSelector(getTimelineEventsRaw)
  const timelineTripsSensors = useTypedSelector(getTimelineTripsSensors)
  const preferences = useTypedSelector(getPreferences)
  const hardwareTypeList = useTypedSelector(getHardwareTypeList)
  const focusedVehicleId = focusedItem.id
  const selectedTripStartDate = useTypedSelector(getSelectedTripStartDate)

  const sectionType = bottomPanelState.sectionType

  const [timelineStyle, setTimelineStyle] = useState(() => {
    const height = TIMELINE_MIN_HEIGHT_MAP['vehicles'][sectionType] + 'px'
    return {
      height,
      minHeight: height,
    }
  })
  const [timelineHeightChange, setTimelineHeightChange] = useState(false)
  const [timelineHeight, setTimelineHeight] = useState<string | undefined>(undefined)
  const mapEl: HTMLElement | null = useMemo(() => document.querySelector('.Map'), [])

  const hardwareType = useTimelineHardwareTypeQuery({
    vehicleId: focusedVehicleId,
  })

  // get the table type based on the hardware type and list
  const tableType = useMemo(() => {
    if (hardwareType.status === 'success') {
      const { hardwareTypeListId, hardwareTypeId } = hardwareType.data

      if (
        isNil(hardwareTypeList) ||
        isNil(hardwareTypeListId) ||
        isNil(hardwareTypeId)
      ) {
        return 'default'
      }

      const hardwareTypeData = hardwareTypeList[hardwareTypeListId].hardwareTypeData

      let isBasic = true

      if (hardwareTypeData[hardwareTypeId]) {
        isBasic = hardwareTypeData[hardwareTypeId].isBasic ?? true
      }

      return isBasic ? 'basic' : 'default'
    }

    return 'default'
  }, [hardwareType, hardwareTypeList])

  const tableId = vehicleTableId[tableType]

  // update the map height
  useEffect(() => {
    const availableHeight = window.innerHeight
    const timelineHeight = timelineStyle.height.slice(0, -2) as FixMeAny

    const parsedTableColumnsVisibility = preferences[tableId]?.columnVisibility

    //Update user settings with new table columns when available
    const hasNewTableKey = Object.keys(tableColumnsVisibility).some(
      (key) => !parsedTableColumnsVisibility?.[key],
    )
    if (hasNewTableKey) {
      dispatch(
        savePreferences(tableId, {
          columnVisibility: {
            ...tableColumnsVisibility,
            ...parsedTableColumnsVisibility,
          },
        }),
      )
    }

    onChangeMapHeight(100 - Math.round((timelineHeight / availableHeight) * 100) + '%')
    return () => {
      onChangeMapHeight('100%')
    }
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const prevSensorFields = usePrevious(timelineTripsSensors.defaultSensorFields)
  const prevPreferences = usePrevious(preferences)

  useEffect(() => {
    const { defaultSensorFields } = timelineTripsSensors
    if (getIsTimelineTripsView(mapType)) {
      if (isNil(preferences[tableId])) {
        // save the columns visibility in specified field to avoid pollute others
        dispatch(
          savePreferences(tableId, {
            columnVisibility: {
              ...tableColumnsVisibility,
              ...defaultSensorFields,
            },
          }),
        )
        return
      }

      const parsedTableColumnsVisibility = preferences[tableId]?.columnVisibility

      // Set Column Visibility Preferences after Sensor Fields loads
      if (
        !isEqual(prevSensorFields, defaultSensorFields) &&
        !isEmpty(defaultSensorFields)
      ) {
        dispatch(
          savePreferences(tableId, {
            columnVisibility: {
              ...parsedTableColumnsVisibility,
              ...defaultSensorFields, // use default to override the vehicle specified sensors
            },
          }),
        )
      }
    } else if (prevPreferences) {
      const prevTableColumnsVisibility = prevPreferences[pointToPointTableId]

      if (isNil(prevTableColumnsVisibility)) {
        dispatch(
          savePreferences(pointToPointTableId, {
            columnVisibility: {
              ...tableColumnsVisibility,
            },
          }),
        )
        return
      }
    }
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timelineTripsSensors.defaultSensorFields, tableId, preferences, mapType])

  const handleEventClick = (clickedEventId: string) => {
    // When we enabled the eslint rule below, there was not enough confidence to use trip equals here
    // eslint-disable-next-line eqeqeq
    const nextIndex = eventsRaw.findIndex((event) => event.id == clickedEventId)
    const nextEvent = eventsRaw[nextIndex]
    const timelineTimeSpan =
      timelineTimeRange.endDateTime.toMillis() -
      timelineTimeRange.startDateTime.toMillis()
    if (nextEvent) {
      const nextProgress =
        (nextEvent.time - timelineTimeRange.startDateTime.toMillis()) / timelineTimeSpan

      if (
        nextEvent.position == null ||
        (nextEvent.position.visibility === 'PUBLIC' &&
          nextEvent.position.principal.description !== 'No GPS')
      ) {
        dispatch(
          jumpToTime({
            nextProgress,
            nextActiveEventIndex: nextIndex,
            nextCoords: {
              lat: nextEvent.lat,
              lng: nextEvent.lng,
            },
          }),
        )
      }
    }
  }

  const handleMouseMove = throttle((e) => {
    const height = Math.max(
      window.innerHeight - e.pageY,
      TIMELINE_MIN_HEIGHT_MAP['vehicles'][sectionType],
    )
    const { timeline, map } = resolveHeightPercentage(height)

    setTimelineStyle({
      ...timelineStyle,
      height: timeline,
    })
    setTimelineHeight(timeline)

    if (!timelineHeightChange) {
      setTimelineHeightChange(true)
    }

    onChangeMapHeight(map)
  }, 30)

  const handleResizeMouseDown = () => {
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleResizeMouseUp)

    // Prevent weird dragging behavior over map and selecting text in timeline
    if (mapEl) {
      mapEl.style.pointerEvents = 'none'
    }
  }

  const handleResizeMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleResizeMouseUp)

    if (mapEl) {
      mapEl.style.pointerEvents = null as FixMeAny
    }
  }

  const handleResizeTimelineHeight = (selectedView: MapActiveView) => {
    const newHeight = TIMELINE_MIN_HEIGHT_MAP['vehicles'][selectedView]
    const { timeline, map } = resolveHeightPercentage(newHeight)

    onChangeMapHeight(map)

    const timelineStyle = {
      minHeight: newHeight + 'px',
      height:
        selectedView === 'timeline' || !timelineHeightChange
          ? timeline
          : (timelineHeight as FixMeAny),
    }

    setTimelineStyle(timelineStyle)
  }

  const handleTripViewClick = (newView: MapActiveView) => {
    if (newView !== sectionType) {
      dispatch(changeTimelineTablesActiveTab('all-trips'))

      handleResizeTimelineHeight(newView)
    }
  }

  if (!focusedItem || !selectedTripStartDate) {
    return null
  }

  return (
    <Timeline sx={timelineStyle}>
      <div
        className="Resizer"
        onMouseDown={handleResizeMouseDown}
        onMouseUp={handleResizeMouseUp}
      />
      <MapBottomPanelTripTablesProvider>
        <ReTimelineHeader
          bottomPanelState={bottomPanelState}
          onTripViewClick={handleTripViewClick}
          focusedItem={focusedItem}
          selectedTripStartDate={selectedTripStartDate}
          showRawData={/^grab/gi.test(username)} // RMM: Temporary handling of when to show raw data page
        />
        {(() => {
          switch (sectionType) {
            case 'timeline': {
              return (
                <ReTimelineSlider
                  events={timelineSliderEvents}
                  timeRange={timelineTimeRange}
                  timelineBarUI={timelineBarUI}
                />
              )
            }
            case 'graph': {
              return <ReTimelineChart />
            }
            case 'table': {
              return (
                <TimelineContainer>
                  <ReTimelineTables
                    selectedTripStartDate={selectedTripStartDate}
                    onEventClick={handleEventClick}
                    focusedItem={focusedItem}
                    initialSelectedTripId={bottomPanelState.initialSelectedTripId}
                  />
                </TimelineContainer>
              )
            }
            default: {
              return null
            }
          }
        })()}
      </MapBottomPanelTripTablesProvider>
    </Timeline>
  )
}

export default ReTimeline

/**
 * Remove sensor options if sensor does not exist in vehicle
 */
export function filterTableColumnOptions<T extends Record<string, FixMeAny>>(
  tableVisibility: T,
  tableColumns: TypedColumns<FixMeAny>,
) {
  return Object.keys(tableVisibility).reduce<T>((acc, unTypedKey) => {
    const key = unTypedKey as keyof T
    const isValidHeader = tableColumns.some((column) => isEqual(column.Header, key))

    if (isValidHeader) {
      acc[key] = tableVisibility[key]
    }

    return acc
  }, {} as T)
}

const TimelineContainer = styled(Stack)({
  height: `calc(100% - ${TABLE_TIMELINE_HEADER_HEIGHT + MARGIN_BOTTOM}px)`,
  justifyContent: 'space-between',
  minHeight: '130px',
  overflow: 'auto',
  position: 'relative',
  paddingBottom: '1px', // To show the border of the timeline table
  paddingTop: '1px',
})

const Timeline = styled(Box)({
  position: 'absolute',
  width: '100%',
  zIndex: '100',
  boxShadow: '0 1px 0 0 #eee',
  bottom: 0,
  background: '#fff',
  display: 'flex',
  flexDirection: 'column',
})
