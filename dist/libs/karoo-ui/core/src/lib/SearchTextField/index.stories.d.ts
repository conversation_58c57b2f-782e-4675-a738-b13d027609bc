declare const _default: {
    component: import("react").ForwardRefExoticComponent<Pick<import("type-fest").Except<import("@mui/material").StandardTextFieldProps | import("@mui/material").OutlinedTextFieldProps, "onChange">, "sx" | "variant" | "label" | "onClick" | "size" | "autoFocus" | "placeholder" | "fullWidth"> & {
        value: string;
        searchIconProps?: import("react").ComponentProps<import("@mui/material/OverridableComponent").OverridableComponent<import("@mui/material").SvgIconTypeMap<{}, "svg">> & {
            muiName: string;
        }>;
        onClearIconClick: import("react").MouseEventHandler<HTMLButtonElement>;
        onChange: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
    } & import("react").RefAttributes<import("react").Ref<any> | undefined>>;
};
export default _default;
export declare const Default: () => import("react/jsx-runtime").JSX.Element;
export declare const Standard: () => import("react/jsx-runtime").JSX.Element;
