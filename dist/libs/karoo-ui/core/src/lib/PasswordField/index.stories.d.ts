declare const _default: {
    component: import("react").ForwardRefExoticComponent<Omit<{
        className?: string | undefined;
        style?: React.CSSProperties | undefined;
        classes?: Partial<import("@mui/material").TextFieldClasses> | undefined;
        children?: import("react").ReactNode;
        sx?: import("@mui/system").SxProps<import("@mui/material").Theme> | undefined;
        variant?: "standard" | "outlined" | "filled" | undefined;
        rows?: string | number | undefined;
        slotProps?: {
            root?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").FormControlProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            input?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").InputProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            inputLabel?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").InputLabelProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            htmlInput?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").InputBaseComponentProps | undefined, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            formHelperText?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").FormHelperTextProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            select?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").SelectProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
        } | {
            root?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").FormControlProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            input?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").OutlinedInputProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            inputLabel?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").InputLabelProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            htmlInput?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").InputBaseComponentProps | undefined, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            formHelperText?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").FormHelperTextProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            select?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").SelectProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
        } | {
            root?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").FormControlProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            input?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").FilledInputProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            inputLabel?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").InputLabelProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            htmlInput?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").InputBaseComponentProps | undefined, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            formHelperText?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").FormHelperTextProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
            select?: import("@mui/material").SlotProps<import("react").ElementType<import("@mui/material").SelectProps, keyof import("react").JSX.IntrinsicElements>, {}, import("@mui/material").BaseTextFieldProps> | undefined;
        } | undefined;
        onResize?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        "aria-label"?: string | undefined | undefined;
        "aria-labelledby"?: string | undefined | undefined;
        label?: import("react").ReactNode;
        nonce?: string | undefined | undefined;
        slots?: Partial<import("@mui/material").TextFieldSlots> | undefined;
        color?: import("@mui/types").OverridableStringUnion<"primary" | "secondary" | "error" | "info" | "success" | "warning", import("@mui/material").TextFieldPropsColorOverrides> | undefined;
        content?: string | undefined | undefined;
        translate?: "yes" | "no" | undefined | undefined;
        margin?: "dense" | "normal" | "none" | undefined;
        error?: boolean | undefined;
        id?: string | undefined;
        dangerouslySetInnerHTML?: {
            __html: string | TrustedHTML;
        } | undefined | undefined;
        onCopy?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
        onCopyCapture?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
        onCut?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
        onCutCapture?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
        onPaste?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
        onPasteCapture?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
        onCompositionEnd?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
        onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
        onCompositionStart?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
        onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
        onCompositionUpdate?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
        onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
        onFocus?: import("react").FocusEventHandler<HTMLInputElement | HTMLTextAreaElement> | undefined;
        onFocusCapture?: import("react").FocusEventHandler<HTMLDivElement> | undefined;
        onBlur?: import("react").FocusEventHandler<HTMLInputElement | HTMLTextAreaElement> | undefined;
        onBlurCapture?: import("react").FocusEventHandler<HTMLDivElement> | undefined;
        onChange?: import("react").ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement> | undefined;
        onChangeCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onBeforeInput?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onBeforeInputCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onInput?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onInputCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onReset?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onResetCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onSubmit?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onSubmitCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onInvalid?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onInvalidCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
        onLoad?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onLoadCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onError?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onErrorCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onKeyDown?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
        onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
        onKeyPress?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
        onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
        onKeyUp?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
        onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
        onAbort?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onAbortCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onCanPlay?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onCanPlayCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onCanPlayThrough?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onDurationChange?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onDurationChangeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onEmptied?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onEmptiedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onEncrypted?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onEncryptedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onEnded?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onEndedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onLoadedData?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onLoadedDataCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onLoadedMetadata?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onLoadStart?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onLoadStartCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onPause?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onPauseCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onPlay?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onPlayCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onPlaying?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onPlayingCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onProgress?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onProgressCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onRateChange?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onRateChangeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onResizeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onSeeked?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onSeekedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onSeeking?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onSeekingCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onStalled?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onStalledCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onSuspend?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onSuspendCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onTimeUpdate?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onVolumeChange?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onWaiting?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onWaitingCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onAuxClick?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onAuxClickCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onClick?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onClickCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onContextMenu?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onContextMenuCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onDoubleClick?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onDoubleClickCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onDrag?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragEnd?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragEndCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragEnter?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragEnterCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragExit?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragExitCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragLeave?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragLeaveCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragOver?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragOverCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragStart?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDragStartCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDrop?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onDropCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
        onMouseDown?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseDownCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseEnter?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseLeave?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseMove?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseMoveCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseOut?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseOutCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseOver?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseOverCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseUp?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onMouseUpCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
        onSelect?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onSelectCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
        onTouchCancel?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onTouchCancelCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onTouchEnd?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onTouchEndCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onTouchMove?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onTouchMoveCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onTouchStart?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onTouchStartCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
        onPointerDown?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerDownCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerMove?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerMoveCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerUp?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerUpCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerCancel?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerCancelCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerEnter?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerLeave?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerOver?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerOverCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerOut?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onPointerOutCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onGotPointerCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onLostPointerCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
        onScroll?: import("react").UIEventHandler<HTMLDivElement> | undefined;
        onScrollCapture?: import("react").UIEventHandler<HTMLDivElement> | undefined;
        onWheel?: import("react").WheelEventHandler<HTMLDivElement> | undefined;
        onWheelCapture?: import("react").WheelEventHandler<HTMLDivElement> | undefined;
        onAnimationStart?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
        onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
        onAnimationEnd?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
        onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
        onAnimationIteration?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
        onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
        onTransitionEnd?: import("react").TransitionEventHandler<HTMLDivElement> | undefined;
        onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLDivElement> | undefined;
        ref?: ((instance: HTMLDivElement | null) => void | import("react").DO_NOT_USE_OR_YOU_WILL_BE_FIRED_CALLBACK_REF_RETURN_VALUES[keyof import("react").DO_NOT_USE_OR_YOU_WILL_BE_FIRED_CALLBACK_REF_RETURN_VALUES]) | import("react").RefObject<HTMLDivElement> | null | undefined;
        disabled?: boolean | undefined;
        role?: import("react").AriaRole | undefined;
        size?: import("@mui/types").OverridableStringUnion<"small" | "medium", import("@mui/material").TextFieldPropsSizeOverrides> | undefined;
        tabIndex?: number | undefined | undefined;
        title?: string | undefined | undefined;
        component?: React.ElementType | undefined;
        autoFocus?: boolean | undefined;
        value?: unknown;
        slot?: string | undefined | undefined;
        select?: boolean | undefined;
        key?: import("react").Key | null | undefined;
        defaultChecked?: boolean | undefined | undefined;
        defaultValue?: unknown;
        suppressContentEditableWarning?: boolean | undefined | undefined;
        suppressHydrationWarning?: boolean | undefined | undefined;
        accessKey?: string | undefined | undefined;
        contentEditable?: "inherit" | (boolean | "true" | "false") | "plaintext-only" | undefined;
        contextMenu?: string | undefined | undefined;
        dir?: string | undefined | undefined;
        draggable?: (boolean | "true" | "false") | undefined;
        hidden?: boolean | undefined | undefined;
        lang?: string | undefined | undefined;
        spellCheck?: (boolean | "true" | "false") | undefined;
        radioGroup?: string | undefined | undefined;
        about?: string | undefined | undefined;
        datatype?: string | undefined | undefined;
        inlist?: any;
        prefix?: string | undefined | undefined;
        property?: string | undefined | undefined;
        rel?: string | undefined | undefined;
        resource?: string | undefined | undefined;
        rev?: string | undefined | undefined;
        typeof?: string | undefined | undefined;
        vocab?: string | undefined | undefined;
        autoCapitalize?: string | undefined | undefined;
        autoCorrect?: string | undefined | undefined;
        autoSave?: string | undefined | undefined;
        itemProp?: string | undefined | undefined;
        itemScope?: boolean | undefined | undefined;
        itemType?: string | undefined | undefined;
        itemID?: string | undefined | undefined;
        itemRef?: string | undefined | undefined;
        results?: number | undefined | undefined;
        security?: string | undefined | undefined;
        unselectable?: "on" | "off" | undefined | undefined;
        inputMode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search" | undefined | undefined;
        is?: string | undefined | undefined;
        "aria-activedescendant"?: string | undefined | undefined;
        "aria-atomic"?: (boolean | "true" | "false") | undefined;
        "aria-autocomplete"?: "none" | "inline" | "list" | "both" | undefined | undefined;
        "aria-braillelabel"?: string | undefined | undefined;
        "aria-brailleroledescription"?: string | undefined | undefined;
        "aria-busy"?: (boolean | "true" | "false") | undefined;
        "aria-checked"?: boolean | "false" | "mixed" | "true" | undefined | undefined;
        "aria-colcount"?: number | undefined | undefined;
        "aria-colindex"?: number | undefined | undefined;
        "aria-colindextext"?: string | undefined | undefined;
        "aria-colspan"?: number | undefined | undefined;
        "aria-controls"?: string | undefined | undefined;
        "aria-current"?: boolean | "false" | "true" | "page" | "step" | "location" | "date" | "time" | undefined | undefined;
        "aria-describedby"?: string | undefined | undefined;
        "aria-description"?: string | undefined | undefined;
        "aria-details"?: string | undefined | undefined;
        "aria-disabled"?: (boolean | "true" | "false") | undefined;
        "aria-dropeffect"?: "none" | "copy" | "execute" | "link" | "move" | "popup" | undefined | undefined;
        "aria-errormessage"?: string | undefined | undefined;
        "aria-expanded"?: (boolean | "true" | "false") | undefined;
        "aria-flowto"?: string | undefined | undefined;
        "aria-grabbed"?: (boolean | "true" | "false") | undefined;
        "aria-haspopup"?: boolean | "false" | "true" | "menu" | "listbox" | "tree" | "grid" | "dialog" | undefined | undefined;
        "aria-hidden"?: (boolean | "true" | "false") | undefined;
        "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined | undefined;
        "aria-keyshortcuts"?: string | undefined | undefined;
        "aria-level"?: number | undefined | undefined;
        "aria-live"?: "off" | "assertive" | "polite" | undefined | undefined;
        "aria-modal"?: (boolean | "true" | "false") | undefined;
        "aria-multiline"?: (boolean | "true" | "false") | undefined;
        "aria-multiselectable"?: (boolean | "true" | "false") | undefined;
        "aria-orientation"?: "horizontal" | "vertical" | undefined | undefined;
        "aria-owns"?: string | undefined | undefined;
        "aria-placeholder"?: string | undefined | undefined;
        "aria-posinset"?: number | undefined | undefined;
        "aria-pressed"?: boolean | "false" | "mixed" | "true" | undefined | undefined;
        "aria-readonly"?: (boolean | "true" | "false") | undefined;
        "aria-relevant"?: "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text" | "text additions" | "text removals" | undefined | undefined;
        "aria-required"?: (boolean | "true" | "false") | undefined;
        "aria-roledescription"?: string | undefined | undefined;
        "aria-rowcount"?: number | undefined | undefined;
        "aria-rowindex"?: number | undefined | undefined;
        "aria-rowindextext"?: string | undefined | undefined;
        "aria-rowspan"?: number | undefined | undefined;
        "aria-selected"?: (boolean | "true" | "false") | undefined;
        "aria-setsize"?: number | undefined | undefined;
        "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined | undefined;
        "aria-valuemax"?: number | undefined | undefined;
        "aria-valuemin"?: number | undefined | undefined;
        "aria-valuenow"?: number | undefined | undefined;
        "aria-valuetext"?: string | undefined | undefined;
        name?: string | undefined;
        autoComplete?: string | undefined;
        placeholder?: string | undefined;
        required?: boolean | undefined;
        fullWidth?: boolean | undefined;
        focused?: boolean | undefined;
        hiddenLabel?: boolean | undefined;
        FormHelperTextProps?: Partial<import("@mui/material").FormHelperTextProps> | undefined;
        helperText?: import("react").ReactNode;
        InputLabelProps?: Partial<import("@mui/material").InputLabelProps> | undefined;
        inputProps?: import("@mui/material").InputBaseComponentProps | undefined;
        inputRef?: React.Ref<any> | undefined;
        multiline?: boolean | undefined;
        maxRows?: string | number | undefined;
        minRows?: string | number | undefined;
        SelectProps?: Partial<import("@mui/material").SelectProps> | undefined;
        readOnly?: boolean | undefined;
    } & {
        showPassword?: boolean;
    }, "ref"> & import("react").RefAttributes<HTMLDivElement>>;
};
export default _default;
export declare const Default: () => import("react/jsx-runtime").JSX.Element;
export declare const Standard: () => import("react/jsx-runtime").JSX.Element;
export declare const WithCustomIcon: () => import("react/jsx-runtime").JSX.Element;
export declare const WithoutIcon: () => import("react/jsx-runtime").JSX.Element;
