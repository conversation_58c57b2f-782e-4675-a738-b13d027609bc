import type { GridInitialState as GridInitialStatePremium } from '@mui/x-data-grid-premium';
import type { Except } from 'type-fest';
import { type DataGridProps, type GridValidRowModel } from '../DataGrid';
import type { StorageDataGridId } from './types';
import type { DataGridInitialConfigToSaveAllVersions, DataGridInitialConfigToSaveLatestVersion, DataGridInitialStateToSave } from './utils';
export type StorageAccessors = {
    update(updater: (oldValue: DataGridInitialConfigToSaveAllVersions | undefined) => DataGridInitialConfigToSaveLatestVersion): Promise<void>;
};
export type RemainingInitialState = Except<GridInitialStatePremium, keyof DataGridInitialStateToSave>;
export type DataGridWithSavedSettingsProps<R extends GridValidRowModel> = Except<DataGridProps<R>, 'initialState'> & {
    storageDataGridId: StorageDataGridId;
    storageAccessors: StorageAccessors;
    initialStateFromStorage: DataGridInitialStateToSave | undefined;
    Component: React.ComponentType<DataGridProps<R>>;
    remainingInitialState: RemainingInitialState | undefined;
};
export declare function DataGridWithSavedSettings<R extends GridValidRowModel>({ storageDataGridId: _storageDataGridId, storageAccessors, initialStateFromStorage, apiRef, Component, remainingInitialState, ...restProps }: DataGridWithSavedSettingsProps<R>): import("react/jsx-runtime").JSX.Element;
