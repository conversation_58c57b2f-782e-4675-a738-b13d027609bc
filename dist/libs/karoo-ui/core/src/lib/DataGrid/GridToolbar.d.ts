import { type ButtonProps } from '@mui/material/Button';
import { type SxProps } from '@mui/material/styles';
import { type GridToolbarExportProps, type GridToolbarFilterButtonProps, type GridToolbarQuickFilterProps } from '@mui/x-data-grid-premium';
import { type SearchTextFieldProps } from '../SearchTextField';
import type { TablePaginationOwnProps, TablePaginationSlotsAndSlotProps } from '../TablePagination';
declare module '@mui/x-data-grid-premium' {
    interface ToolbarPropsOverrides extends GridToolbarStandardOldProps, GridToolbarWithQuickFilterProps {
    }
}
declare module '@mui/x-data-grid' {
    interface PaginationPropsOverrides extends TablePaginationSlotsAndSlotProps, TablePaginationOwnProps {
    }
}
declare function GridToolbarColumnsButton(): import("react/jsx-runtime").JSX.Element;
declare function GridToolbarFilterButton(props: Omit<GridToolbarFilterButtonProps, 'ref'> & React.RefAttributes<HTMLButtonElement>): import("react/jsx-runtime").JSX.Element;
declare function GridToolbarDensitySelector(): import("react/jsx-runtime").JSX.Element;
declare function GridToolbarExport({ slotProps, ...props }: Partial<GridToolbarExportProps>): import("react/jsx-runtime").JSX.Element;
type GridToolbarSearchButtonTextFieldProps = {
    GridToolbarQuickFilterProps?: Pick<GridToolbarQuickFilterProps, 'debounceMs'>;
};
declare function GridToolbarSearchButtonTextField({ GridToolbarQuickFilterProps, }: GridToolbarSearchButtonTextFieldProps): import("react/jsx-runtime").JSX.Element;
type GridToolbarSearchOldProps = {
    SearchTextFieldProps: SearchTextFieldProps;
    SearchButtonProps?: ButtonProps;
};
declare function ToolbarStandardContent(): import("react/jsx-runtime").JSX.Element;
declare const BaseGridToolbarContainer: import("@emotion/styled").StyledComponent<((import("react").HTMLAttributes<HTMLDivElement> & {
    sx?: SxProps<import("@mui/system").Theme>;
} & import("react").RefAttributes<HTMLDivElement>) | (import("react").HTMLAttributes<HTMLDivElement> & {
    sx?: SxProps<import("@mui/system").Theme>;
})) & import("@mui/system").MUIStyledCommonProps<import("@mui/material/styles").Theme>, {}, {}>;
declare const BaseGridToolbarContainerWithItems: import("@emotion/styled").StyledComponent<import("@mui/system").MUIStyledCommonProps<import("@mui/material/styles").Theme>, import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
type GridToolbarWithQuickFilterProps = {
    gridToolbarLeftContent?: React.ReactNode;
    gridToolbarRightContent?: React.ReactNode;
    withExport?: boolean;
    exportProps?: Partial<GridToolbarExportProps>;
    SearchButtonProps?: ButtonProps;
};
/**GridToolbar that uses the __QuickFilter__ (https://next.mui.com/x/react-data-grid/filtering/#quick-filter) which works better for data grids with selection and has better behavior with search, sort and filtering. */
declare function _GridToolbarWithQuickFilter({ gridToolbarLeftContent, gridToolbarRightContent, withExport, exportProps, }: GridToolbarWithQuickFilterProps): import("react/jsx-runtime").JSX.Element;
declare const GridToolbarWithQuickFilter: typeof _GridToolbarWithQuickFilter & {
    createProps: (props?: GridToolbarWithQuickFilterProps) => GridToolbarWithQuickFilterProps;
};
type GridToolbarStandardOldProps = GridToolbarSearchOldProps & {
    gridToolbarRightContent?: React.ReactNode;
    gridToolbarLeftContent?: React.ReactNode;
};
/**This toolbar is using the old way of filtering with a search text field. Please use the __GridToolbarWithQuickFilter__ instead as it was better behavior with search, sort and filtering.*/
declare function _GridToolbarStandardOld({ SearchTextFieldProps, SearchButtonProps, gridToolbarLeftContent, gridToolbarRightContent, }: GridToolbarStandardOldProps): import("react/jsx-runtime").JSX.Element;
declare const GridToolbarStandardOld: typeof _GridToolbarStandardOld & {
    createProps: (props: GridToolbarStandardOldProps) => GridToolbarStandardOldProps;
};
type GridToolbarStandardProps = {
    gridToolbarRightContent?: React.ReactNode;
    gridToolbarLeftContent?: React.ReactNode;
};
declare function _GridToolbarStandard({ gridToolbarLeftContent, gridToolbarRightContent, }: GridToolbarStandardProps): import("react/jsx-runtime").JSX.Element;
declare const GridToolbarStandard: typeof _GridToolbarStandard & {
    createProps: (props: GridToolbarStandardProps) => GridToolbarStandardProps;
};
export { BaseGridToolbarContainer, BaseGridToolbarContainerWithItems, GridToolbarColumnsButton, GridToolbarDensitySelector, GridToolbarExport, GridToolbarFilterButton, GridToolbarSearchButtonTextField, GridToolbarStandard, GridToolbarStandardOld, GridToolbarWithQuickFilter, ToolbarStandardContent, };
export type { GridToolbarSearchButtonTextFieldProps, GridToolbarStandardOldProps, GridToolbarWithQuickFilterProps, };
