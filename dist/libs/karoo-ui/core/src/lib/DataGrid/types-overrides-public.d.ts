import type { GridRowId, GridValidRowModel, GridValueFormatter, GridValueOptionsParams, GridColDef as unsafe_GridColDef, GridRowSelectionModel as unsafe_GridRowSelectionModel } from '@mui/x-data-grid-premium';
import type { GridApiCommunity, GridStateColDef, GridBaseColDef as unsafe_GridBaseColDef } from '@mui/x-data-grid/internals';
import type { Except } from 'type-fest';
import type { ColumnHelperDef } from './public-utils';
import type { GridActionsColDef } from './types-overrides-internal';
type GridBaseColDef<R extends GridValidRowModel, V = unknown, F = V> = Omit<unsafe_GridBaseColDef<R, V, F>, 'valueGetter' | 'valueFormatter'> & {
    /**
     * Function that allows to get a specific data instead of field to render in the cell.
     */
    valueGetter: ((value: never, row: R, column: unsafe_GridColDef<R, any, F>, // Needs to use GridColDef from x-data-grid-premium directly to avoid error types everywhere
    apiRef: React.MutableRefObject<GridApiCommunity>) => V) | undefined;
    /**
     * Function that allows to apply a formatter before rendering its value.
     */
    valueFormatter?: GridValueFormatter<R, V, F, V>;
};
/**
 * Column Definition interface used for columns with the `singleSelect` type.
 *
 * IMPORTANT:
 * This interface is very similar to the original mui-x interface `GridSingleSelectColDef`.
 * Main difference is that it extends from __our__ `GridBaseColDef` instead of the original `GridBaseColDef`.
 * Generics also have less any types.
 */
type GridSingleSelectColDef<ValueOption extends V | {
    value: V;
    label: string;
} | Record<string, unknown>, R extends GridValidRowModel, V = unknown, F = V> = {
    /**
     * The type of the column.
     * @default 'singleSelect'
     */
    type: 'singleSelect';
    /**
     * To be used in combination with `type: 'singleSelect'`. This is an array (or a function returning an array) of the possible cell values and labels.
     */
    valueOptions: ReadonlyArray<ValueOption> | ((params: GridValueOptionsParams<R>) => ReadonlyArray<ValueOption>);
    /**
     * Used to determine the label displayed for a given value option.
     * @param {ValueOptions} value The current value option.
     * @returns {string} The text to be displayed.
     */
    getOptionLabel?: (value: ValueOption) => string;
    /**
     * Used to determine the value used for a value option.
     * @param {ValueOptions} value The current value option.
     * @returns {string} The value to be used.
     */
    getOptionValue?: (value: ValueOption) => V;
} & GridBaseColDef<R, V, F>;
type GridColDef<R extends GridValidRowModel, V = any, F = V> = GridBaseColDef<R, V, F> | ColumnHelperDef<Except<GridBaseColDef<R, V, F>, 'valueGetter'>> | GridActionsColDef<R, V, F> | GridSingleSelectColDef<Record<string, unknown>, R, V, F>;
type GridRowSelectionModel<Id extends GridRowId = GridRowId> = Except<unsafe_GridRowSelectionModel, 'ids'> & {
    ids: ReadonlySet<Id>;
};
export type { GridColDef, GridBaseColDef, GridSingleSelectColDef, GridStateColDef, GridRowSelectionModel, };
