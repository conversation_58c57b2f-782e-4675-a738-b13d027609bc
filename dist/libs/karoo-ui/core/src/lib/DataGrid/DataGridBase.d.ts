import { type DataGridPremiumProps as DataGridMuiProps, type GridCallbackDetails, type GridRowId, type GridValidRowModel } from '@mui/x-data-grid-premium';
import type { Except } from 'type-fest';
import type { GridFilterModel } from './overrides';
import type { ColumnHelperDef } from './public-utils';
import type { GridRowSelectionModel } from './types-overrides-public';
type RemoveArrayFirstElement<A extends Array<any>> = A extends [any, ...infer Rest] ? Rest : [];
export type DataGridBaseProps<R extends GridValidRowModel = any> = Except<DataGridMuiProps<R>, 'columns' | 'onFilterModelChange' | 'rowSelectionModel' | 'onRowSelectionModelChange'> & {
    columns: ReadonlyArray<DataGridMuiProps<R>['columns'][number] | ColumnHelperDef<Except<DataGridMuiProps<R>['columns'][number], 'valueGetter'>>>;
    onFilterModelChange?: (model: GridFilterModel, ...rest: RemoveArrayFirstElement<Parameters<NonNullable<DataGridMuiProps<R>['onFilterModelChange']>>>) => void;
    rowSelectionModel?: GridRowSelectionModel | ReadonlySet<GridRowId>;
    /**
     * Callback fired when the selection state of one or multiple rows changes.
     */
    onRowSelectionModelChange?: (model: GridRowSelectionModel, details: GridCallbackDetails<'rowSelection'>) => void;
    'data-testid'?: string;
};
export declare function DataGridBase<R extends GridValidRowModel>({ slots, slotProps, columns: columnsProp_, sx: sxProp, apiRef: apiRefProp, onFilterModelChange, showToolbar, rowSelectionModel: rowSelectionModelProp, onRowSelectionModelChange: onRowSelectionModelChangeProp, 'data-testid': dataTestId, ...restProps }: DataGridBaseProps<R>): import("react/jsx-runtime").JSX.Element;
export {};
