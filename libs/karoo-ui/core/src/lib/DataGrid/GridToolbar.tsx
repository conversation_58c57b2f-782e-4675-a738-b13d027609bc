import { useState } from 'react'
import SearchRoundedIcon from '@mui/icons-material/SearchRounded'
import Button, { type ButtonProps } from '@mui/material/Button'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import { useThemeProps, type SxProps } from '@mui/material/styles'
import {
  GridCsvExportMenuItem,
  GridExcelExportMenuItem,
  GridPrintExportMenuItem,
  gridQuickFilterValuesSelector,
  GridToolbarContainer,
  GridToolbarExportContainer,
  GridToolbarQuickFilter,
  GridToolbarColumnsButton as MUIGridToolbarColumnsButton,
  GridToolbarDensitySelector as MUIGridToolbarDensitySelector,
  GridToolbarExport as MUIGridToolbarExport,
  GridToolbarFilterButton as MUIGridToolbarFilterButton,
  useGridApiContext,
  type GridToolbarExportProps,
  type GridToolbarFilterButtonProps,
  type GridToolbarQuickFilterProps,
} from '@mui/x-data-grid-premium'
import * as R from 'remeda'

import { Box } from '../Box'
import { SearchTextField, type SearchTextFieldProps } from '../SearchTextField'
import { styled } from '../styled'
import type {
  TablePaginationOwnProps,
  TablePaginationSlotsAndSlotProps,
} from '../TablePagination'
import type { KarooUiInternalTheme } from '../ThemeProvider'

// See https://next.mui.com/x/react-data-grid/components/#custom-slot-props-with-typescript
declare module '@mui/x-data-grid-premium' {
  // oxlint-disable-next-line consistent-type-definitions
  interface ToolbarPropsOverrides
    extends GridToolbarStandardOldProps,
      GridToolbarWithQuickFilterProps {}
}

// Also extend the base @mui/x-data-grid module for completeness
declare module '@mui/x-data-grid' {
  // oxlint-disable-next-line consistent-type-definitions
  interface PaginationPropsOverrides
    extends TablePaginationSlotsAndSlotProps,
      TablePaginationOwnProps {}
}

const standardPropsSx = (({ palette }) => ({
  color: palette.secondary.main,
})) satisfies SxProps<KarooUiInternalTheme>

function GridToolbarColumnsButton() {
  return (
    <MUIGridToolbarColumnsButton
      slotProps={{ button: { material: { sx: standardPropsSx } } }}
    />
  )
}

function GridToolbarFilterButton(
  props: Omit<GridToolbarFilterButtonProps, 'ref'> &
    React.RefAttributes<HTMLButtonElement>,
) {
  const buttonSx = props.slotProps?.button?.material?.sx ?? []

  return (
    <MUIGridToolbarFilterButton
      slotProps={{
        button: {
          ...props.slotProps?.button,
          material: {
            ...props.slotProps?.button?.material,
            sx: [standardPropsSx, ...(R.isArray(buttonSx) ? buttonSx : [buttonSx])],
          },
        },
        ...props,
      }}
    />
  )
}

function GridToolbarDensitySelector() {
  return (
    <MUIGridToolbarDensitySelector
      slotProps={{ button: { material: { sx: standardPropsSx } } }}
    />
  )
}

function GridToolbarExport({ slotProps, ...props }: Partial<GridToolbarExportProps>) {
  const buttonSx = slotProps?.button?.material?.sx ?? []

  return (
    <MUIGridToolbarExport
      {...props}
      {...{ 'data-testid': 'GridToolbarExport' }}
      slotProps={{
        ...slotProps,
        button: {
          ...slotProps?.button,
          material: {
            ...slotProps?.button?.material,
            sx: [standardPropsSx, ...(R.isArray(buttonSx) ? buttonSx : [buttonSx])],
          },
        },
      }}
    />
  )
}

function GridToolbarSearchButton(inButtonProps: ButtonProps) {
  const themeProps = useThemeProps<object, ButtonProps, 'KarooGridToolbarSearchButton'>(
    {
      name: 'KarooGridToolbarSearchButton',
      props: inButtonProps,
    },
  )
  return (
    <Button
      startIcon={<SearchRoundedIcon />}
      size="small"
      variant="text"
      {...standardPropsSx}
      {...themeProps}
    />
  )
}

type GridToolbarSearchButtonTextFieldProps = {
  GridToolbarQuickFilterProps?: Pick<GridToolbarQuickFilterProps, 'debounceMs'>
}

function GridToolbarSearchButtonTextField({
  GridToolbarQuickFilterProps,
}: GridToolbarSearchButtonTextFieldProps) {
  const [showSearchTextField, setShowSearchTextField] = useState(false)

  const apiRef = useGridApiContext()
  const quickFilterValue = gridQuickFilterValuesSelector(apiRef)

  if (showSearchTextField) {
    return (
      <ClickAwayListener
        onClickAway={() => {
          // Hide the search text field on click away if value is empty
          if (quickFilterValue?.length === 0) {
            setShowSearchTextField(false)
          }
        }}
      >
        <Box>
          <GridToolbarQuickFilter
            sx={{
              px: '5px',
              pb: '0px',
              '.MuiSvgIcon-root': {
                height: '18px',
                width: '18px',
                ml: '-2px',
              },
            }}
            variant="standard"
            autoFocus
            placeholder=""
            debounceMs={100}
            {...GridToolbarQuickFilterProps}
          />
        </Box>
      </ClickAwayListener>
    )
  }

  return (
    <GridToolbarSearchButton
      onClick={() => {
        setShowSearchTextField((prev) => !prev)
      }}
      {...standardPropsSx}
    />
  )
}

type GridToolbarSearchOldProps = {
  SearchTextFieldProps: SearchTextFieldProps
  SearchButtonProps?: ButtonProps
}

function GridToolbarSearchOld({
  SearchTextFieldProps: { value, onClearIconClick, ...restSearchTextFieldProps },
  SearchButtonProps: { onClick, ...restSearchButtonProps } = {},
}: GridToolbarSearchOldProps) {
  const [showSearchTextField, setShowSearchTextField] = useState(value.length > 0)

  if (showSearchTextField) {
    return (
      <ClickAwayListener
        onClickAway={() => {
          if (value === '') {
            setShowSearchTextField(false)
          }
        }}
      >
        <SearchTextField
          value={value}
          variant="standard"
          autoFocus
          placeholder=""
          onClearIconClick={(...args) => {
            onClearIconClick(...args)
            setShowSearchTextField(false)
          }}
          {...restSearchTextFieldProps}
          sx={{
            px: '5px',
            '.MuiInput-input': {
              pb: 0.5,
            },
            ...restSearchTextFieldProps.sx,
          }}
          searchIconProps={{
            // hardcoded to avoid layout shifting
            sx: {
              height: '18px',
              width: '18px',
              ml: '-2px',
            },
            ...restSearchTextFieldProps.searchIconProps,
          }}
        />
      </ClickAwayListener>
    )
  }

  return (
    <GridToolbarSearchButton
      onClick={(e) => {
        onClick?.(e)
        setShowSearchTextField((prev) => !prev)
      }}
      {...standardPropsSx}
      {...restSearchButtonProps}
    />
  )
}

function ToolbarStandardContent() {
  return (
    <>
      <GridToolbarColumnsButton data-testid="GridToolbarColumnsButton" />
      <GridToolbarFilterButton data-testid="GridToolbarFilterButton" />
      <GridToolbarDensitySelector data-testid="GridToolbarDensitySelector" />
    </>
  )
}

const BaseGridToolbarContainer = styled(GridToolbarContainer)(({ theme }) =>
  theme.unstable_sx({ p: 1 }),
)

const GridToolbarContainerWithItemsOnBothSides = styled(BaseGridToolbarContainer)`
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const BaseGridToolbarContainerWithItems = styled('div')(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    alignItems: 'center',
    gap: 0.5,
  }),
)

const GridToolbarLeftContainer = styled(BaseGridToolbarContainerWithItems)``

const GridToolbarRightContainer = styled(BaseGridToolbarContainerWithItems)``

type GridToolbarWithQuickFilterProps = {
  gridToolbarLeftContent?: React.ReactNode
  gridToolbarRightContent?: React.ReactNode
  withExport?: boolean
  exportProps?: Partial<GridToolbarExportProps>
  SearchButtonProps?: ButtonProps
}

/**GridToolbar that uses the __QuickFilter__ (https://next.mui.com/x/react-data-grid/filtering/#quick-filter) which works better for data grids with selection and has better behavior with search, sort and filtering. */
function _GridToolbarWithQuickFilter({
  gridToolbarLeftContent,
  gridToolbarRightContent,
  withExport,
  exportProps,
}: GridToolbarWithQuickFilterProps) {
  return (
    <GridToolbarContainerWithItemsOnBothSides data-testid="GridToolbarQF">
      <GridToolbarLeftContainer>
        <ToolbarStandardContent />
        {withExport && (
          <GridToolbarExportContainer
            slotProps={{ button: { material: { color: 'secondary' } } }}
          >
            {!exportProps?.csvOptions?.disableToolbarButton && (
              <GridCsvExportMenuItem
                options={exportProps?.csvOptions}
                data-testid="GridCsvExportMenuItem"
              />
            )}
            {!exportProps?.printOptions?.disableToolbarButton && (
              <GridPrintExportMenuItem
                options={exportProps?.printOptions}
                data-testid="GridPrintExportMenuItem"
              />
            )}
            {!exportProps?.excelOptions?.disableToolbarButton && (
              <GridExcelExportMenuItem
                options={exportProps?.excelOptions}
                data-testid="GridExcelExportMenuItem"
              />
            )}
          </GridToolbarExportContainer>
        )}
        <GridToolbarSearchButtonTextField />
        {gridToolbarLeftContent}
      </GridToolbarLeftContainer>
      <GridToolbarRightContainer data-testid="GridToolbarQFRight">
        {gridToolbarRightContent}
      </GridToolbarRightContainer>
    </GridToolbarContainerWithItemsOnBothSides>
  )
}

const GridToolbarWithQuickFilter = Object.assign(_GridToolbarWithQuickFilter, {
  createProps: (props?: GridToolbarWithQuickFilterProps) => props ?? {},
})

type GridToolbarStandardOldProps = GridToolbarSearchOldProps & {
  gridToolbarRightContent?: React.ReactNode
  gridToolbarLeftContent?: React.ReactNode
}

/**This toolbar is using the old way of filtering with a search text field. Please use the __GridToolbarWithQuickFilter__ instead as it was better behavior with search, sort and filtering.*/
function _GridToolbarStandardOld({
  SearchTextFieldProps,
  SearchButtonProps,
  gridToolbarLeftContent,
  gridToolbarRightContent,
}: GridToolbarStandardOldProps) {
  return (
    <GridToolbarContainerWithItemsOnBothSides>
      <GridToolbarLeftContainer>
        <ToolbarStandardContent />
        <GridToolbarSearchOld
          SearchTextFieldProps={SearchTextFieldProps}
          SearchButtonProps={SearchButtonProps}
        />
        {gridToolbarLeftContent}
      </GridToolbarLeftContainer>
      <GridToolbarRightContainer>{gridToolbarRightContent}</GridToolbarRightContainer>
    </GridToolbarContainerWithItemsOnBothSides>
  )
}

const GridToolbarStandardOld = Object.assign(_GridToolbarStandardOld, {
  createProps: (props: GridToolbarStandardOldProps) => props,
})

type GridToolbarStandardProps = {
  gridToolbarRightContent?: React.ReactNode
  gridToolbarLeftContent?: React.ReactNode
}

function _GridToolbarStandard({
  gridToolbarLeftContent,
  gridToolbarRightContent,
}: GridToolbarStandardProps) {
  return (
    <GridToolbarContainerWithItemsOnBothSides>
      <GridToolbarLeftContainer>
        <ToolbarStandardContent />
        {gridToolbarLeftContent}
      </GridToolbarLeftContainer>
      <GridToolbarRightContainer>{gridToolbarRightContent}</GridToolbarRightContainer>
    </GridToolbarContainerWithItemsOnBothSides>
  )
}

const GridToolbarStandard = Object.assign(_GridToolbarStandard, {
  createProps: (props: GridToolbarStandardProps) => props,
})

export {
  BaseGridToolbarContainer,
  BaseGridToolbarContainerWithItems,
  GridToolbarColumnsButton,
  GridToolbarDensitySelector,
  GridToolbarExport,
  GridToolbarFilterButton,
  GridToolbarSearchButtonTextField,
  GridToolbarStandard,
  GridToolbarStandardOld,
  GridToolbarWithQuickFilter,
  ToolbarStandardContent,
}
export type {
  GridToolbarSearchButtonTextFieldProps,
  GridToolbarStandardOldProps,
  GridToolbarWithQuickFilterProps,
}
