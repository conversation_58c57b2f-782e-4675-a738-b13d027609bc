import { useCallback, useMemo, useState, type ComponentProps } from 'react'
import debounce from 'lodash/debounce'
import InputBase, { type InputBaseProps } from '@mui/material/InputBase'
import type { TextFieldProps } from '@mui/material/TextField'
import {
  GRID_DATE_COL_DEF as GRID_DATE_COL_DEF_OLD,
  GRID_DATETIME_COL_DEF as GRID_DATETIME_COL_DEF_OLD,
  useGridApiContext,
  type GetApplyFilterFn,
  type GridColDef,
  type GridColType,
  type GridColTypeDef,
  type GridFilterInputValueProps,
  type GridFilterItem,
  type GridFilterOperator,
  type GridRenderEditCellParams,
  type GridValidRowModel,
} from '@mui/x-data-grid-premium'
import type { GridApiCommunity } from '@mui/x-data-grid/internals'
import {
  DatePicker,
  DateRangePicker,
  DateTimePicker,
  SingleInputDateRangeField,
  type DatePickerProps,
  type DateRangePickerProps,
  type DateRangeValidationError,
  type DateTimePickerProps,
  type DateTimeValidationError,
  type DateValidationError,
  type PickerChangeHandlerContext,
  type PickerValidDate,
} from '@mui/x-date-pickers-pro'
import * as R from 'remeda'
import type { Except } from 'type-fest'

import {
  useKarooExtendedLocales,
  type KarooExtendedLocalesContextValue,
} from '../KarooExtendedLocalesContext'
import { styled } from '../styled'
import { usePickersAdapterContextUtils } from '../usePickersAdapterContextUtils'
import { CellTextOverflowTypography } from './CellTextOverflowTypography'
import type { GridBaseColDef } from './types-overrides-public'

// The mui team already plans to remove support for "string" values in the future so we should remove this option right now to avoid breaking changes in the future.
// This way, only "Date" values are supported.
const GRID_DATETIME_COL_DEF = GRID_DATETIME_COL_DEF_OLD as GridColTypeDef<
  Date | null | undefined,
  string
>
const GRID_DATE_COL_DEF = GRID_DATE_COL_DEF_OLD as GridColTypeDef<
  Date | null | undefined,
  string
>

/** We created these types to deviate from standard a few behaviors of mui date and dateTime columns:
 *  - Export in excel - mui exports out of the box "date" and "dateTime" type columns in a way that messes up the timezone
 */
const KAROO_DATETIME_COLUMN_TYPE = 'karoo-datetime' as GridColType
const KAROO_DATE_COLUMN_TYPE = 'karoo-date' as GridColType

// Utilities inspired by https://mui.com/x/react-data-grid/recipes-editing/#usage-with-mui-x-date-pickers

type PickersAdapterUtils = ReturnType<typeof usePickersAdapterContextUtils>

function buildApplyDateFilterFn({
  compareFn,
  filterItem: { value: filterItemValue_ },
  showTime = false,
  pickersAdapterUtils,
}: {
  filterItem: GridFilterItem
  compareFn: (value1: number, value2: number) => boolean
  showTime: boolean
  pickersAdapterUtils: PickersAdapterUtils
}): ReturnType<GetApplyFilterFn<GridValidRowModel, Date | null | undefined, unknown>> {
  const filterItemValue = filterItemValue_ as PickerValidDate | null | undefined
  if (!filterItemValue) {
    return null
  }

  const createDateForComparison = (adapterDate: PickerValidDate): Date => {
    if (showTime) {
      const adapterDateWithZeroSeconds = pickersAdapterUtils.setSeconds(adapterDate, 0)
      const date = pickersAdapterUtils.toJsDate(adapterDateWithZeroSeconds)
      date.setMilliseconds(0) // pickersAdapterUtils does not support setMilliseconds so we need to use the native Date object

      return date
    }
    return pickersAdapterUtils.toJsDate(pickersAdapterUtils.startOfDay(adapterDate))
  }

  const filterValueMs = createDateForComparison(filterItemValue).getTime()

  return (value) => {
    if (!value) {
      return false
    }

    const cellValueAsDate = createDateForComparison(
      pickersAdapterUtils.date(value.toISOString()),
    )
    const cellValueMs = cellValueAsDate.getTime()

    return compareFn(cellValueMs, filterValueMs)
  }
}

function getGridDateColumnFilterOperators({
  showTime,
  pickersAdapterUtils,
  filterMode,
  extendedLocales,
}: {
  showTime: boolean
  pickersAdapterUtils: PickersAdapterUtils
  filterMode: 'server' | 'client'
  extendedLocales: KarooExtendedLocalesContextValue
}) {
  const inputComponent = GridFilterDateInput

  const inputComponentProps = {
    showTime,
    filterMode,
  } satisfies Partial<ComponentProps<typeof inputComponent>>

  return [
    {
      value: 'range' as const,
      label: extendedLocales.filterOperators.date_range,
      headerLabel: extendedLocales.filterOperators.date_range,
      getApplyFilterFn: (filterItem_) => {
        const filterItemValue = filterItem_.value as unknown
        if (!filterItemValue) {
          return null
        }
        if (!R.isArray(filterItemValue)) {
          return null
        }
        if (filterItemValue.length !== 2) {
          return null
        }

        const [startFilterValue, endFilterValue] = filterItemValue as [
          PickerValidDate | null | undefined,
          PickerValidDate | null | undefined,
        ]

        const filterRangeInMs = {
          start: R.isNullish(startFilterValue)
            ? null
            : pickersAdapterUtils
                .toJsDate(pickersAdapterUtils.startOfDay(startFilterValue))
                .getTime(),
          end: R.isNullish(endFilterValue)
            ? null
            : pickersAdapterUtils
                .toJsDate(pickersAdapterUtils.endOfDay(endFilterValue))
                .getTime(),
        }

        return (value) => {
          if (!value) {
            return false
          }

          const cellValueMs = value.getTime()

          return (
            (filterRangeInMs.start === null
              ? true
              : cellValueMs >= filterRangeInMs.start) &&
            (filterRangeInMs.end === null ? true : cellValueMs <= filterRangeInMs.end)
          )
        }
      },
      InputComponent: GridFilterDateRangeInput,
      InputComponentProps: {} satisfies Partial<
        ComponentProps<typeof GridFilterDateRangeInput>
      >,
    } satisfies GridFilterOperator<
      Record<string, unknown>,
      Date | null | undefined,
      unknown,
      ComponentProps<typeof GridFilterDateRangeInput>
    >,

    {
      value: 'is',
      getApplyFilterFn: (filterItem) => {
        return buildApplyDateFilterFn({
          filterItem,
          compareFn: (value1, value2) => value1 === value2,
          showTime,
          pickersAdapterUtils,
        })
      },
      InputComponent: inputComponent,
      InputComponentProps: inputComponentProps,
    },
    {
      value: 'not',
      getApplyFilterFn: (filterItem) => {
        return buildApplyDateFilterFn({
          filterItem,
          compareFn: (value1, value2) => value1 !== value2,
          showTime,
          pickersAdapterUtils,
        })
      },
      InputComponent: inputComponent,
      InputComponentProps: inputComponentProps,
    },
    {
      value: 'after',
      getApplyFilterFn: (filterItem) => {
        return buildApplyDateFilterFn({
          filterItem,
          compareFn: (value1, value2) => value1 > value2,
          showTime,
          pickersAdapterUtils,
        })
      },
      InputComponent: inputComponent,
      InputComponentProps: inputComponentProps,
    },
    {
      value: 'onOrAfter',
      getApplyFilterFn: (filterItem) => {
        return buildApplyDateFilterFn({
          filterItem,
          compareFn: (value1, value2) => value1 >= value2,
          showTime,
          pickersAdapterUtils,
        })
      },
      InputComponent: inputComponent,
      InputComponentProps: inputComponentProps,
    },
    {
      value: 'before',
      getApplyFilterFn: (filterItem) => {
        return buildApplyDateFilterFn({
          filterItem,
          compareFn: (value1, value2) => value1 < value2,
          showTime,
          pickersAdapterUtils,
        })
      },
      InputComponent: inputComponent,
      InputComponentProps: inputComponentProps,
    },
    {
      value: 'onOrBefore',
      getApplyFilterFn: (filterItem) => {
        return buildApplyDateFilterFn({
          filterItem,
          compareFn: (value1, value2) => value1 <= value2,
          showTime,
          pickersAdapterUtils,
        })
      },
      InputComponent: inputComponent,
      InputComponentProps: inputComponentProps,
    },
    {
      value: 'isEmpty',
      getApplyFilterFn: () => {
        return (value): boolean => {
          return value == null
        }
      },
      requiresFilterValue: false,
    },
    {
      value: 'isNotEmpty',
      getApplyFilterFn: () => {
        return (value): boolean => {
          return value != null
        }
      },
      requiresFilterValue: false,
    },
  ] as const satisfies ReadonlyArray<
    GridFilterOperator<
      Record<string, unknown>,
      Date | null | undefined,
      unknown,
      ComponentProps<typeof inputComponent>
    >
  >
}

type GridDateColumnFilterOperator = ReturnType<
  typeof getGridDateColumnFilterOperators
>[number]['value']

const GridEditDateInput = styled(InputBase)({
  fontSize: 'inherit',
  padding: '0 9px',
})

function WrappedGridEditDateInput(props: TextFieldProps) {
  const { InputProps, ...other } = props
  return (
    <GridEditDateInput
      fullWidth
      {...InputProps}
      {...(other as InputBaseProps)}
    />
  )
}

function GridEditDateCell({
  id,
  field,
  value: valueProp,
  colDef,
}: GridRenderEditCellParams<GridValidRowModel, Date | null | undefined, unknown>) {
  const apiRef = useGridApiContext()
  const utils = usePickersAdapterContextUtils()

  const handleChange = (newValue: PickerValidDate | null | undefined) => {
    apiRef.current.setEditCellValue({
      id,
      field,
      value: R.isNullish(newValue) ? newValue : utils.toJsDate(newValue),
    })
  }

  const Component =
    colDef.type === KAROO_DATETIME_COLUMN_TYPE ? DateTimePicker : DatePicker

  const parsedValue = useMemo((): PickerValidDate | null | undefined => {
    if (R.isNullish(valueProp)) {
      return valueProp
    }

    return utils.date(valueProp.toISOString())
  }, [utils, valueProp])

  return (
    <Component
      value={parsedValue}
      autoFocus
      onChange={handleChange}
      slots={{ textField: WrappedGridEditDateInput }}
    />
  )
}

type GridFilterDateInputProps = GridFilterInputValueProps & {
  showTime?: boolean
  filterMode: 'server' | 'client'
}

function GridFilterDateInput({
  item,
  showTime,
  applyValue,
  apiRef,
  filterMode,
}: GridFilterDateInputProps) {
  const [isPickerOpen, setIsPickerOpen] = useState(false)

  const onChangeHandler = useMemo(() => {
    const handleFilterChange_ = (
      newValue: unknown,
      context:
        | PickerChangeHandlerContext<DateValidationError>
        | PickerChangeHandlerContext<DateTimeValidationError>,
    ) => {
      if (context.validationError === null) {
        applyValue({ ...item, value: newValue })
      }
    }

    if (isPickerOpen) {
      // Do nothing when the picker is open. onAccept will take care of it
      return undefined
    }

    switch (filterMode) {
      case 'client': {
        return handleFilterChange_
      }
      case 'server': {
        // When the date is changed through the input, we want to debounce the change (prevent multiple requests)
        return debounce(handleFilterChange_, 600)
      }
    }
  }, [applyValue, filterMode, isPickerOpen, item])

  const onAcceptHandler = useCallback(
    (newValue: unknown) => {
      applyValue({ ...item, value: newValue })
    },
    [applyValue, item],
  )

  const baseProps = {
    open: isPickerOpen,
    onOpen: () => setIsPickerOpen(true),
    onClose: () => setIsPickerOpen(false),
    onAccept: onAcceptHandler,
    value: item.value || null,
    onChange: onChangeHandler,
    autoFocus: true,
    label: apiRef.current.getLocaleText('filterPanelInputLabel'),
    slotProps: {
      textField: {
        variant: 'standard',
      },
      inputAdornment: {
        sx: {
          '& .MuiButtonBase-root': {
            marginRight: -1,
          },
        },
      },
    },
  } satisfies DateTimePickerProps<PickerValidDate> | DatePickerProps<PickerValidDate>

  return showTime ? (
    <DateTimePicker
      {...baseProps}
      closeOnSelect={false}
      timeSteps={{
        hours: 1,
        minutes: 1,
      }}
    />
  ) : (
    <DatePicker {...baseProps} />
  )
}

type GridFilterDateRangeInputProps = GridFilterInputValueProps & {
  dateRangePickerProps?: Partial<DateRangePickerProps<PickerValidDate>>
  variant?: TextFieldProps['variant']
  size?: TextFieldProps['size']
}

function GridFilterDateRangeInput({
  item,
  applyValue,
  apiRef,
  variant = 'outlined',
  size = 'small',
  dateRangePickerProps,
}: GridFilterDateRangeInputProps) {
  const onChangeHandler = useMemo(() => {
    const handleFilterChange_ = (
      newValue: unknown,
      context: PickerChangeHandlerContext<DateRangeValidationError>,
    ) => {
      if (
        context.validationError === null &&
        context.validationError[0] === null &&
        context.validationError[1] === null
      ) {
        applyValue({ ...item, value: newValue })
      }
    }

    return handleFilterChange_
  }, [applyValue, item])

  const onAcceptHandler = useCallback(
    (newValue: unknown) => {
      applyValue({ ...item, value: newValue })
    },
    [applyValue, item],
  )

  const baseProps = {
    onAccept: onAcceptHandler,
    /** Our users prefer this behaviour and is less confusing */
    disableAutoMonthSwitching: true,
    value: item.value || [null, null],
    onChange: onChangeHandler,
    autoFocus: true,
    label: apiRef.current.getLocaleText('filterPanelInputLabel'),
    slotProps: {
      textField: { variant, size },
      field: {
        slotProps: { textField: { size } },
      },
      actionBar: { actions: ['clear'] },
    },
    // In order to see the full date
    sx: { minWidth: 224 },
    slots: { field: SingleInputDateRangeField },
    ...dateRangePickerProps,
  } satisfies DateRangePickerProps<PickerValidDate>

  return <DateRangePicker {...baseProps} />
}

type DataGridDateColDef<R extends GridValidRowModel, V, F> = Except<
  GridBaseColDef<R, V, F>,
  // We should not override these values since it may interfere with the column functionality
  'valueFormatter' | 'type' | 'sortComparator' | 'align' | 'valueGetter'
> & {
  // We explicitly set the valueGetter type for the same reason we do it on GridBaseColDef. Check the docs there.
  valueGetter: (
    value: never,
    row: R,
    column: GridColDef<R, any, F>,
    apiRef: React.MutableRefObject<GridApiCommunity>,
  ) => V
  valueFormatter?: (
    value: V,
    row: R,
    apiRef: React.MutableRefObject<GridApiCommunity>,
    extraInfo: { defaultFormatter: (date: Date) => string },
  ) => F
}

// Currently the same as DataGridDateColDefParam but it can defer in the future. That's why we have a separate type.
type DataGridDateTimeColDef<R extends GridValidRowModel, V, F> = DataGridDateColDef<
  R,
  V,
  F
>

/**
 * @deprecated Use `columnHelper.date()` or `columnHelper.dateTime()` from `useDataGridColumnHelper` hook, instead.
 */
function useDataGridDateColumns({ filterMode }: { filterMode: 'server' | 'client' }) {
  const utils = usePickersAdapterContextUtils()
  const extendedLocales = useKarooExtendedLocales()

  return useMemo(() => {
    const dateTimeColDefaultFormatter = (dateValue: Date) =>
      utils.format(utils.date(dateValue.toISOString()), 'keyboardDateTime')

    const getGridDateColumnOperators = ({ showTime }: { showTime: boolean }) => {
      return getGridDateColumnFilterOperators({
        showTime,
        pickersAdapterUtils: utils,
        filterMode,
        extendedLocales,
      })
    }

    function createDateTimeColumn<
      R extends GridValidRowModel,
      V extends Date | null | undefined,
      F extends string | number | null,
    >({
      valueFormatter,
      renderCell,
      ...columnDefinition
    }: DataGridDateTimeColDef<R, V, F>) {
      const defaultFormatter = dateTimeColDefaultFormatter

      return {
        ...(GRID_DATETIME_COL_DEF as unknown as GridColTypeDef<V, F>),
        type: KAROO_DATETIME_COLUMN_TYPE,
        // Can still be overwritten if needed
        width: DATAGRID_DATETIME_COLUMN_WIDTH,
        resizable: false,
        renderEditCell: (params) => {
          return <GridEditDateCell {...params} />
        },
        filterOperators: getGridDateColumnOperators({
          showTime: true,
        }) as unknown as GridColTypeDef['filterOperators'],
        valueFormatter: (value, row, _column, apiRef) => {
          if (valueFormatter) {
            return valueFormatter(value, row, apiRef, { defaultFormatter })
          }

          if (value) {
            return defaultFormatter(value) as F
          }
          return '' as F
        },
        renderCell:
          renderCell ??
          (({ formattedValue }) => (
            <CellTextOverflowTypography>{formattedValue}</CellTextOverflowTypography>
          )),
        ...columnDefinition,

        /* Last tested with "@mui/x-data-grid-premium": "6.3.0" */
        // Set default __groupable__ and __aggregable__ to false by default. These features are not yet stable to be used by default for __every__ column.
        // We still give the option to enable them by explicitly setting the `groupable` and `aggregable` props to true for specific columns.
        groupable: columnDefinition.groupable ?? false,
        aggregable: columnDefinition.aggregable ?? false,
      } satisfies GridBaseColDef<R, V, F>
    }

    const dateColDefaultFormatter = (dateValue: Date) =>
      utils.format(utils.date(dateValue.toISOString()), 'keyboardDate')

    function createDateColumn<
      R extends GridValidRowModel,
      V extends Date | null | undefined,
      F extends string | number | null,
    >({
      valueFormatter,
      renderCell,
      ...columnDefinition
    }: DataGridDateColDef<R, V, F>) {
      const defaultFormatter = dateColDefaultFormatter

      return {
        ...(GRID_DATE_COL_DEF as unknown as GridColTypeDef<V, F>),
        type: KAROO_DATE_COLUMN_TYPE,
        resizable: false,
        renderEditCell: (params) => {
          return <GridEditDateCell {...params} />
        },
        filterOperators: getGridDateColumnOperators({
          showTime: false,
        }) as unknown as GridColTypeDef['filterOperators'],
        valueFormatter: (value, row, _column, apiRef) => {
          if (valueFormatter) {
            return valueFormatter(value, row, apiRef, { defaultFormatter })
          }

          if (value) {
            return defaultFormatter(value) as F
          }
          return '' as F
        },
        renderCell:
          renderCell ??
          (({ formattedValue }) => (
            <CellTextOverflowTypography>{formattedValue}</CellTextOverflowTypography>
          )),
        ...columnDefinition,

        /* Last tested with "@mui/x-data-grid-premium": "6.3.0" */
        // Set default __groupable__ and __aggregable__ to false by default. These features are not yet stable to be used by default for __every__ column.
        // We still give the option to enable them by explicitly setting the `groupable` and `aggregable` props to true for specific columns.
        groupable: columnDefinition.groupable ?? false,
        aggregable: columnDefinition.aggregable ?? false,
      } satisfies GridBaseColDef<R, V, F>
    }

    return {
      createDateTimeColumn,
      createDateColumn,
      dateTimeColDefaultFormatter,
      dateColDefaultFormatter,
      getGridDateColumnOperators,
    }
  }, [extendedLocales, filterMode, utils])
}

/**
 * Minimum width to make sure that __all__ default formats for different locales are not overflown by default
 */
const DATAGRID_DATETIME_COLUMN_WIDTH = 175

export {
  useDataGridDateColumns,
  GridFilterDateRangeInput,
  DATAGRID_DATETIME_COLUMN_WIDTH,
}

export type {
  GridDateColumnFilterOperator,
  GridFilterDateRangeInputProps,
  DataGridDateColDef,
  DataGridDateTimeColDef,
}
