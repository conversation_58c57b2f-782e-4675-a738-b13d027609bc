import { useMemo, useState } from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import { expect, userEvent, within } from '@storybook/test'
import { useDemoData } from '@mui/x-data-grid-generator'
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon'

import { Chip } from '../Chip'
import { LocalizationProvider } from '../LocalizationProvider'
import { useSearchTextField } from '../SearchTextField'
import { ContainerWithTabsForDataGrid } from './ContainerWithTabsForDataGrid'
import { GridToolbarStandardOld, GridToolbarWithQuickFilter } from './GridToolbar'
import {
  DataGrid,
  DataGridAsTabItem,
  getGridSingleSelectOperators,
  useDataGridColumnHelper,
  type GridColDef,
} from './index'

export default {
  component: DataGrid,
} as Meta<typeof DataGrid>

/** A more complete and best practice focused example will be provided in the future */
export const Default = () => {
  const { data } = useDemoData({
    dataSet: 'Employee',
    rowLength: 100_000,
  })

  return (
    <div style={{ height: 520, width: '100%' }}>
      <DataGrid {...data} />
    </div>
  )
}

export const WithOldStandardToolbarPlay: StoryObj<typeof DataGrid> = {
  render: () => <WithOldStandardToolbar withDemoData={false} />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    await userEvent.click(canvas.getByText(/Search/i))
  },
}

export const WithOldStandardToolbar = ({
  withDemoData = true,
}: {
  withDemoData?: boolean
}) => {
  const {
    data: { rows, ...data },
  } = useDemoData({
    dataSet: 'Employee',
    rowLength: 1_000,
  })
  const searchProps = useSearchTextField('')

  const filteredRows = useMemo(() => {
    // NOTE - this is NOT the best way to do filtering. It's just for demo purposes
    if (searchProps.value === '') {
      return rows
    }

    return rows.filter((row) =>
      Object.values(row).some((value) => {
        if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          return JSON.stringify(value).toLowerCase().includes(searchProps.value)
        }
        return false
      }),
    )
  }, [rows, searchProps.value])

  return (
    <div style={{ height: 520, width: '100%' }}>
      <DataGrid
        {...(withDemoData
          ? { ...data, rows: filteredRows }
          : { columns: [], rows: [] })}
        slots={{
          toolbar: GridToolbarStandardOld,
        }}
        slotProps={{
          toolbar: GridToolbarStandardOld.createProps({
            SearchTextFieldProps: searchProps,
          }),
        }}
      />
    </div>
  )
}

export const WithMuiXDatePickers: StoryObj<typeof DataGrid> = {
  decorators: [
    (Story) => (
      <LocalizationProvider
        dateAdapter={AdapterLuxon}
        adapterLocale="en-US"
      >
        <Story />
      </LocalizationProvider>
    ),
  ],
  render: () => <WithMuiXDatePickersComponent />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    await userEvent.click(canvas.getByLabelText(/Show filters/i))
  },
}

const WithMuiXDatePickersComponent = () => {
  type Row = {
    id: number
    name: string
    dateOfBirth: Date
    dateTimeOfBirth: Date | null
    dateWithCustomFormat: Date | null
    age: number | null
  }
  const columnHelper = useDataGridColumnHelper<Row>({ filterMode: 'client' })
  const searchProps = useSearchTextField('')

  const rows = useMemo((): Array<Row> => {
    return [
      {
        id: 1,
        name: 'John Doe',
        dateOfBirth: new Date(2022, 4, 17, 6),
        dateTimeOfBirth: new Date(2022, 4, 17, 6),
        dateWithCustomFormat: new Date(2023, 4, 17, 6),
        age: 21,
      },
      {
        id: 2,
        name: 'Jane Doe These are some long names to test the overflow',
        dateOfBirth: new Date(2022, 3, 18, 5),
        dateTimeOfBirth: new Date(2022, 3, 18, 5),
        dateWithCustomFormat: null,
        age: 3012314123123132,
      },
      {
        id: 3,
        name: 'Rod Doe',
        dateOfBirth: new Date(2022, 3, 18, 5),
        dateTimeOfBirth: null,
        dateWithCustomFormat: new Date(2023, 2, 11, 0),
        age: null,
      },
    ]
  }, [])

  const columns = useMemo((): Array<GridColDef<(typeof rows)[number]>> => {
    return [
      {
        field: 'id',
        valueGetter: (_, row) => row.id,
        filterable: false,
      },
      columnHelper.string((_, row) => row.name, {
        headerName: 'Name',
        field: 'name',
        renderCell: ({ value }) => {
          return value
        },
      }),
      columnHelper.date({
        headerName: 'Date of Birth',
        field: 'dateOfBirth',
        // Leave these extra arguments here to test if type inference still works
        valueGetter: (_, row, _col, _apiRef): Date | null | undefined =>
          row.dateOfBirth,
        width: 150,
        editable: true,
      }),
      columnHelper.singleSelect((_) => 'cavals' as const, {
        // @ts-expect-error - Should error because we are returning `cavals`, which is not valid.
        valueOptions: ['cavalos', 'egua'],
        filterOperators: getGridSingleSelectOperators<Row, 'cavals' | 'egua'>(),
        renderCell: ({ value }) => {
          return <p>{value}</p>
        },
        headerName: 'Is Active',
        field: 'isActive',
      }),
      columnHelper.dateTime({
        headerName: 'Date Time of Birth',
        field: 'dateTimeOfBirth',
        valueGetter: (_, row) => row.dateTimeOfBirth,
        editable: true,
      }),
      columnHelper.dateTime({
        headerName: 'Date Time with custom format',
        field: 'dateTimeWithCustomFormat',
        valueGetter: (_, row) => row.dateWithCustomFormat,
        valueFormatter: (value, _row, _apiRef, { defaultFormatter }) => {
          if (!value) {
            return null
          }
          return defaultFormatter(value)
        },
        renderCell: ({ formattedValue }) => {
          return formattedValue === null ? (
            <Chip label="Not available" />
          ) : (
            formattedValue
          )
        },
        editable: true,
      }),
      columnHelper.date({
        headerName: 'Date with custom format',
        field: 'dateWithCustomFormat',
        resizable: true,
        minWidth: 130,
        valueGetter: (_, row) => row.dateWithCustomFormat,
        valueFormatter: (value, _row, _apiRef, { defaultFormatter }) => {
          if (!value) {
            return null
          }
          return defaultFormatter(value)
        },
        renderCell: ({ formattedValue }) => {
          return formattedValue === null ? (
            <Chip label="Not available" />
          ) : (
            formattedValue
          )
        },
        editable: true,
      }),
      columnHelper.number((_, row) => row.age, {
        headerName: 'Age',
        field: 'age',
        renderCell: ({ value }) => {
          return value
        },
      }),
    ]
  }, [columnHelper])

  const filteredRows = useMemo(() => {
    // NOTE - this is NOT the best way to do filtering. It's just for demo purposes
    if (searchProps.value === '') {
      return rows
    }

    return rows.filter((row) =>
      Object.values(row).some((value) => {
        if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          return JSON.stringify(value).toLowerCase().includes(searchProps.value)
        }
        return false
      }),
    )
  }, [rows, searchProps.value])

  return (
    <div style={{ height: 520, width: '100%' }}>
      <DataGrid
        rows={filteredRows}
        columns={columns}
        slots={{
          toolbar: GridToolbarStandardOld,
        }}
        slotProps={{
          toolbar: GridToolbarStandardOld.createProps({
            SearchTextFieldProps: searchProps,
          }),
        }}
      />
    </div>
  )
}

export const WithContainerWithTabsForDataGrid = () => {
  const searchProps1 = useSearchTextField('')
  const searchProps2 = useSearchTextField('')
  const [selectedTab, setSelectedTab] = useState<1 | 2>(1)

  const rows1 = useMemo((): Array<{
    id: number
    name: string
  }> => {
    return [
      { id: 1, name: 'John Doe' },
      { id: 2, name: 'Jane Doe' },
    ]
  }, [])

  const rows2 = useMemo((): Array<{
    id: number
    value: number
  }> => {
    return [
      { id: 1, value: 10000 },
      { id: 2, value: 20023 },
    ]
  }, [])

  const columns1 = useMemo((): Array<GridColDef<(typeof rows1)[number]>> => {
    return [
      {
        field: 'id',
        valueGetter: (_, row) => row.id,
        filterable: false,
      },
      {
        headerName: 'Name',
        field: 'name',
        valueGetter: (_, row) => row.name,
      },
    ]
  }, [])

  const columns2 = useMemo((): Array<GridColDef<(typeof rows2)[number]>> => {
    return [
      {
        field: 'id',
        valueGetter: (_, row) => row.id,
        filterable: false,
      },
      {
        headerName: 'Value',
        field: 'value',
        valueGetter: (_, row) => row.value,
      },
    ]
  }, [])

  const filteredRows1 = useMemo(() => {
    // NOTE - this is NOT the best way to do filtering. It's just for demo purposes
    if (searchProps1.value === '') {
      return rows1
    }

    return rows1.filter((row) =>
      Object.values(row).some((value) => {
        if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          return JSON.stringify(value).toLowerCase().includes(searchProps1.value)
        }
        return false
      }),
    )
  }, [rows1, searchProps1.value])

  const filteredRows2 = useMemo(() => {
    // NOTE - this is NOT the best way to do filtering. It's just for demo purposes
    if (searchProps2.value === '') {
      return rows2
    }

    return rows2.filter((row) =>
      Object.values(row).some((value) => {
        if (
          typeof value === 'string' ||
          typeof value === 'number' ||
          typeof value === 'boolean'
        ) {
          return JSON.stringify(value).toLowerCase().includes(searchProps2.value)
        }
        return false
      }),
    )
  }, [rows2, searchProps2.value])

  return (
    <div style={{ height: 520, width: '100%' }}>
      <ContainerWithTabsForDataGrid
        renderTabs={() => (
          <ContainerWithTabsForDataGrid.Tabs
            value={selectedTab}
            onChange={(_e, newValue: typeof selectedTab) => {
              setSelectedTab(newValue)
            }}
          >
            {[
              { label: 'Tab 1', value: 1 },
              { label: 'Tab 2', value: 2 },
            ].map(({ label, value }) => (
              <ContainerWithTabsForDataGrid.Tab
                key={value}
                label={label}
                value={value}
              />
            ))}
          </ContainerWithTabsForDataGrid.Tabs>
        )}
      >
        {selectedTab === 1 && (
          <DataGridAsTabItem
            rows={filteredRows1}
            columns={columns1}
            slots={{
              toolbar: GridToolbarStandardOld,
            }}
            slotProps={{
              toolbar: GridToolbarStandardOld.createProps({
                SearchTextFieldProps: searchProps1,
              }),
            }}
          />
        )}
        {selectedTab === 2 && (
          <DataGridAsTabItem
            rows={filteredRows2}
            columns={columns2}
            slots={{
              toolbar: GridToolbarStandardOld,
            }}
            slotProps={{
              toolbar: GridToolbarStandardOld.createProps({
                SearchTextFieldProps: searchProps2,
              }),
            }}
          />
        )}
      </ContainerWithTabsForDataGrid>
    </div>
  )
}

// ----------- NEW

export const WithStandardToolbarPlay: StoryObj<typeof DataGrid> = {
  render: () => <WithStandardToolbar withDemoData={false} />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    await userEvent.click(canvas.getByText(/Search/i))
  },
}

export const WithStandardToolbar = ({
  withDemoData = true,
}: {
  withDemoData?: boolean
}) => {
  const { data } = useDemoData({
    dataSet: 'Employee',
    rowLength: 1_000,
  })

  return (
    <div style={{ height: 520, width: '100%' }}>
      <DataGrid
        {...(withDemoData ? data : { columns: [], rows: [] })}
        slots={{ toolbar: GridToolbarWithQuickFilter }}
        slotProps={{ toolbar: GridToolbarWithQuickFilter.createProps() }}
      />
    </div>
  )
}

export const WithStandardToolbarAndMuiXDatePickers: StoryObj<typeof DataGrid> = {
  decorators: [
    (Story) => (
      <LocalizationProvider
        dateAdapter={AdapterLuxon}
        adapterLocale="en-US"
      >
        <Story />
      </LocalizationProvider>
    ),
  ],
  render: () => <WithStandardToolbarAndMuiXDatePickersComponent />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    const searchButton = canvas.getByText(/SEARCH/i)
    await userEvent.click(
      await canvas.findByLabelText(/Select all rows/i, { selector: 'input' }),
    )
    await userEvent.click(searchButton)
    await userEvent.type(searchButton, 'ThisIsARandomString')
    expect(canvas.getByText(/rows selected/i)).toBeDefined()
  },
}

const WithStandardToolbarAndMuiXDatePickersComponent = () => {
  type Row = {
    id: number
    name: string
    dateOfBirth: Date
    dateTimeOfBirth: Date
    age: number
  }

  const columnHelper = useDataGridColumnHelper<Row>({ filterMode: 'client' })

  const rows = useMemo((): Array<Row> => {
    return [
      {
        id: 1,
        name: 'Rony Doe These are some long names to test the overflow',
        dateOfBirth: new Date(2022, 3, 17, 6),
        dateTimeOfBirth: new Date(2022, 3, 17, 6),
        age: 30,
      },
      {
        id: 2,
        name: 'Jane Doe',
        dateOfBirth: new Date(2022, 3, 18, 5),
        dateTimeOfBirth: new Date(2022, 3, 18, 5),
        age: 21,
      },
    ]
  }, [])

  const columns = useMemo((): Array<GridColDef<(typeof rows)[number]>> => {
    return [
      columnHelper.valueGetter((_, row) => row.id, {
        field: 'id',
        filterable: false,
      }),
      columnHelper.string((_, row) => row.name, {
        headerName: 'Name',
        field: 'name',
        filterable: false,
      }),
      columnHelper.date({
        headerName: 'Date of Birth',
        field: 'dateOfBirth',
        valueGetter: (_, row) => row.dateOfBirth,
        width: 150,
        editable: true,
      }),
      columnHelper.dateTime({
        headerName: 'Date Time of Birth',
        field: 'dateTimeOfBirth',
        valueGetter: (_, row) => row.dateTimeOfBirth,
        editable: true,
      }),
      columnHelper.number((_, row) => row.age, {
        headerName: 'Age',
        field: 'age',
      }),
      columnHelper.boolean((_, row) => row.age > 18, {
        headerName: 'Is Adult',
        field: 'isAdult',
      }),
    ]
  }, [columnHelper])

  return (
    <div style={{ height: 520, width: '100%' }}>
      <DataGrid
        rows={rows}
        columns={columns}
        slots={{
          toolbar: GridToolbarWithQuickFilter,
        }}
        checkboxSelection
        slotProps={{
          toolbar: GridToolbarWithQuickFilter.createProps(),
        }}
      />
    </div>
  )
}
