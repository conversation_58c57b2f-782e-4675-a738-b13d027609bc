// -------------------------------------------------------
// THESE TYPES SHOULD NOT TO BE IMPORTED FROM OUTSIDE OF KAROO-UI !!
// -------------------------------------------------------

import type { GridValidRowModel } from '@mui/x-data-grid-premium'
// eslint-disable-next-line no-restricted-imports
import type {
  // These are mui private types that ideally should not be imported directly but we need them for internal overrides
  GridActionsColDef as unsafe_GridActionsColDef,
} from '@mui/x-data-grid/models/colDef'

type GridActionsColDef<
  R extends GridValidRowModel,
  V = any,
  F = V,
> = unsafe_GridActionsColDef<R, V, F>

export type { GridActionsColDef }
